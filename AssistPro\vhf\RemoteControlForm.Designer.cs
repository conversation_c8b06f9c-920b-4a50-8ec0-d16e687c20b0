﻿namespace AssistPro.vhf
{
    partial class RemoteControlForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.toolStrip1 = new System.Windows.Forms.ToolStrip();
            this.toolStripLabel_com = new System.Windows.Forms.ToolStripLabel();
            this.cb_comport = new System.Windows.Forms.ToolStripComboBox();
            this.cb_baudrate = new System.Windows.Forms.ToolStripComboBox();
            this.btn_ConnectIO = new System.Windows.Forms.ToolStripButton();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.toolStripLabel2 = new System.Windows.Forms.ToolStripLabel();
            this.ts_ip_addr = new System.Windows.Forms.ToolStripTextBox();
            this.toolStripLabel3 = new System.Windows.Forms.ToolStripLabel();
            this.ts_port = new System.Windows.Forms.ToolStripTextBox();
            this.ts_ip_connect = new System.Windows.Forms.ToolStripButton();
            this.gb_rmt_serial = new System.Windows.Forms.GroupBox();
            this.rb_iww_region = new System.Windows.Forms.RadioButton();
            this.rb_usa_region = new System.Windows.Forms.RadioButton();
            this.rb_canada_region = new System.Windows.Forms.RadioButton();
            this.label2 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.lb_tx_freq = new System.Windows.Forms.Label();
            this.bt_ch_up = new System.Windows.Forms.Button();
            this.bt_ch_down = new System.Windows.Forms.Button();
            this.tb_channel = new System.Windows.Forms.TextBox();
            this.lb_rx_freq = new System.Windows.Forms.Label();
            this.lb_channetable = new System.Windows.Forms.ListBox();
            this.rb_intl_region = new System.Windows.Forms.RadioButton();
            this.lb_sql_value = new System.Windows.Forms.Label();
            this.lb_sql = new System.Windows.Forms.Label();
            this.tb_squelch = new System.Windows.Forms.TrackBar();
            this.lb_vol_value = new System.Windows.Forms.Label();
            this.lb_volume = new System.Windows.Forms.Label();
            this.tb_rmt_vol = new System.Windows.Forms.TrackBar();
            this.rb_dsc_mark = new System.Windows.Forms.RadioButton();
            this.rb_dsc_space = new System.Windows.Forms.RadioButton();
            this.rb_dsc_out_off = new System.Windows.Forms.RadioButton();
            this.gb_knob = new System.Windows.Forms.GroupBox();
            this.lb_hds_vol = new System.Windows.Forms.Label();
            this.tb_rmt_hds_vol = new System.Windows.Forms.TrackBar();
            this.lb_hds_vol_value = new System.Windows.Forms.Label();
            this.gb_power = new System.Windows.Forms.GroupBox();
            this.rb_25w = new System.Windows.Forms.RadioButton();
            this.rb_1w = new System.Windows.Forms.RadioButton();
            this.gb_service = new System.Windows.Forms.GroupBox();
            this.cb_dsc_ber_test_mode = new System.Windows.Forms.CheckBox();
            this.cb_dsc_signal_routing = new System.Windows.Forms.CheckBox();
            this.gb_dsc_test_pattern = new System.Windows.Forms.GroupBox();
            this.rb_dsc_dot = new System.Windows.Forms.RadioButton();
            this.cb_PTT = new System.Windows.Forms.CheckBox();
            this.cb_DistBtn = new System.Windows.Forms.CheckBox();
            this.Distress_timer = new System.Windows.Forms.Timer(this.components);
            this.toolStrip1.SuspendLayout();
            this.gb_rmt_serial.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.tb_squelch)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tb_rmt_vol)).BeginInit();
            this.gb_knob.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.tb_rmt_hds_vol)).BeginInit();
            this.gb_power.SuspendLayout();
            this.gb_service.SuspendLayout();
            this.gb_dsc_test_pattern.SuspendLayout();
            this.SuspendLayout();
            // 
            // toolStrip1
            // 
            this.toolStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripLabel_com,
            this.cb_comport,
            this.cb_baudrate,
            this.btn_ConnectIO,
            this.toolStripSeparator1,
            this.toolStripLabel2,
            this.ts_ip_addr,
            this.toolStripLabel3,
            this.ts_port,
            this.ts_ip_connect});
            this.toolStrip1.Location = new System.Drawing.Point(0, 0);
            this.toolStrip1.Name = "toolStrip1";
            this.toolStrip1.Size = new System.Drawing.Size(1002, 25);
            this.toolStrip1.TabIndex = 0;
            this.toolStrip1.Text = "toolStrip1";
            // 
            // toolStripLabel_com
            // 
            this.toolStripLabel_com.Font = new System.Drawing.Font("휴먼모음T", 9F);
            this.toolStripLabel_com.Name = "toolStripLabel_com";
            this.toolStripLabel_com.Size = new System.Drawing.Size(32, 22);
            this.toolStripLabel_com.Text = "COM";
            // 
            // cb_comport
            // 
            this.cb_comport.AutoSize = false;
            this.cb_comport.DropDownWidth = 80;
            this.cb_comport.Margin = new System.Windows.Forms.Padding(5, 0, 5, 0);
            this.cb_comport.Name = "cb_comport";
            this.cb_comport.Size = new System.Drawing.Size(80, 23);
            // 
            // cb_baudrate
            // 
            this.cb_baudrate.AutoSize = false;
            this.cb_baudrate.DropDownWidth = 80;
            this.cb_baudrate.Items.AddRange(new object[] {
            "115200",
            "38400",
            "9600",
            "4800"});
            this.cb_baudrate.Name = "cb_baudrate";
            this.cb_baudrate.Size = new System.Drawing.Size(80, 23);
            this.cb_baudrate.Text = "9600";
            // 
            // btn_ConnectIO
            // 
            this.btn_ConnectIO.AutoSize = false;
            this.btn_ConnectIO.BackColor = System.Drawing.Color.Silver;
            this.btn_ConnectIO.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text;
            this.btn_ConnectIO.Font = new System.Drawing.Font("휴먼모음T", 9F);
            this.btn_ConnectIO.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.btn_ConnectIO.Name = "btn_ConnectIO";
            this.btn_ConnectIO.Size = new System.Drawing.Size(80, 22);
            this.btn_ConnectIO.Text = "CONNECT";
            this.btn_ConnectIO.Click += new System.EventHandler(this.btn_ConnectIO_Click);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Margin = new System.Windows.Forms.Padding(5, 0, 5, 0);
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(6, 25);
            // 
            // toolStripLabel2
            // 
            this.toolStripLabel2.Font = new System.Drawing.Font("휴먼모음T", 9F);
            this.toolStripLabel2.Name = "toolStripLabel2";
            this.toolStripLabel2.Size = new System.Drawing.Size(19, 22);
            this.toolStripLabel2.Text = "IP";
            // 
            // ts_ip_addr
            // 
            this.ts_ip_addr.Font = new System.Drawing.Font("휴먼모음T", 9F);
            this.ts_ip_addr.Name = "ts_ip_addr";
            this.ts_ip_addr.Size = new System.Drawing.Size(80, 25);
            this.ts_ip_addr.Text = "***********";
            // 
            // toolStripLabel3
            // 
            this.toolStripLabel3.Font = new System.Drawing.Font("휴먼모음T", 9F);
            this.toolStripLabel3.Margin = new System.Windows.Forms.Padding(5, 1, 0, 2);
            this.toolStripLabel3.Name = "toolStripLabel3";
            this.toolStripLabel3.Size = new System.Drawing.Size(37, 22);
            this.toolStripLabel3.Text = "PORT";
            // 
            // ts_port
            // 
            this.ts_port.Font = new System.Drawing.Font("휴먼모음T", 9F);
            this.ts_port.Name = "ts_port";
            this.ts_port.Size = new System.Drawing.Size(80, 25);
            this.ts_port.Text = "60001";
            // 
            // ts_ip_connect
            // 
            this.ts_ip_connect.AutoSize = false;
            this.ts_ip_connect.BackColor = System.Drawing.Color.Silver;
            this.ts_ip_connect.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text;
            this.ts_ip_connect.Font = new System.Drawing.Font("휴먼모음T", 9F);
            this.ts_ip_connect.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.ts_ip_connect.Name = "ts_ip_connect";
            this.ts_ip_connect.Size = new System.Drawing.Size(80, 22);
            this.ts_ip_connect.Text = "CONNECT";
            // 
            // gb_rmt_serial
            // 
            this.gb_rmt_serial.BackColor = System.Drawing.Color.Transparent;
            this.gb_rmt_serial.Controls.Add(this.rb_iww_region);
            this.gb_rmt_serial.Controls.Add(this.rb_usa_region);
            this.gb_rmt_serial.Controls.Add(this.rb_canada_region);
            this.gb_rmt_serial.Controls.Add(this.label2);
            this.gb_rmt_serial.Controls.Add(this.label1);
            this.gb_rmt_serial.Controls.Add(this.lb_tx_freq);
            this.gb_rmt_serial.Controls.Add(this.bt_ch_up);
            this.gb_rmt_serial.Controls.Add(this.bt_ch_down);
            this.gb_rmt_serial.Controls.Add(this.tb_channel);
            this.gb_rmt_serial.Controls.Add(this.lb_rx_freq);
            this.gb_rmt_serial.Controls.Add(this.lb_channetable);
            this.gb_rmt_serial.Controls.Add(this.rb_intl_region);
            this.gb_rmt_serial.Font = new System.Drawing.Font("휴먼모음T", 9F);
            this.gb_rmt_serial.Location = new System.Drawing.Point(12, 45);
            this.gb_rmt_serial.Name = "gb_rmt_serial";
            this.gb_rmt_serial.Size = new System.Drawing.Size(365, 447);
            this.gb_rmt_serial.TabIndex = 1;
            this.gb_rmt_serial.TabStop = false;
            this.gb_rmt_serial.Text = "CHANNEL ";
            // 
            // rb_iww_region
            // 
            this.rb_iww_region.AutoSize = true;
            this.rb_iww_region.Location = new System.Drawing.Point(251, 30);
            this.rb_iww_region.Name = "rb_iww_region";
            this.rb_iww_region.Size = new System.Drawing.Size(49, 17);
            this.rb_iww_region.TabIndex = 18;
            this.rb_iww_region.Text = "IWW";
            this.rb_iww_region.UseVisualStyleBackColor = true;
            this.rb_iww_region.Click += new System.EventHandler(this.rb_region_checked_changed);
            // 
            // rb_usa_region
            // 
            this.rb_usa_region.AutoSize = true;
            this.rb_usa_region.Location = new System.Drawing.Point(182, 31);
            this.rb_usa_region.Name = "rb_usa_region";
            this.rb_usa_region.Size = new System.Drawing.Size(47, 17);
            this.rb_usa_region.TabIndex = 17;
            this.rb_usa_region.Text = "USA";
            this.rb_usa_region.UseVisualStyleBackColor = true;
            this.rb_usa_region.Click += new System.EventHandler(this.rb_region_checked_changed);
            // 
            // rb_canada_region
            // 
            this.rb_canada_region.AutoSize = true;
            this.rb_canada_region.Location = new System.Drawing.Point(89, 31);
            this.rb_canada_region.Name = "rb_canada_region";
            this.rb_canada_region.Size = new System.Drawing.Size(69, 17);
            this.rb_canada_region.TabIndex = 16;
            this.rb_canada_region.Text = "CANADA";
            this.rb_canada_region.UseVisualStyleBackColor = true;
            this.rb_canada_region.Click += new System.EventHandler(this.rb_region_checked_changed);
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Font = new System.Drawing.Font("휴먼모음T", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.label2.Location = new System.Drawing.Point(131, 266);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(32, 21);
            this.label2.TabIndex = 15;
            this.label2.Text = "TX";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Font = new System.Drawing.Font("휴먼모음T", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.label1.Location = new System.Drawing.Point(2, 266);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(34, 21);
            this.label1.TabIndex = 14;
            this.label1.Text = "RX";
            // 
            // lb_tx_freq
            // 
            this.lb_tx_freq.AutoSize = true;
            this.lb_tx_freq.Font = new System.Drawing.Font("휴먼모음T", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.lb_tx_freq.Location = new System.Drawing.Point(132, 297);
            this.lb_tx_freq.Name = "lb_tx_freq";
            this.lb_tx_freq.Size = new System.Drawing.Size(61, 18);
            this.lb_tx_freq.TabIndex = 13;
            this.lb_tx_freq.Text = "0.0MHz";
            // 
            // bt_ch_up
            // 
            this.bt_ch_up.Location = new System.Drawing.Point(135, 187);
            this.bt_ch_up.Name = "bt_ch_up";
            this.bt_ch_up.Size = new System.Drawing.Size(116, 53);
            this.bt_ch_up.TabIndex = 12;
            this.bt_ch_up.Text = "UP";
            this.bt_ch_up.UseVisualStyleBackColor = true;
            this.bt_ch_up.Click += new System.EventHandler(this.bt_ch_up_Click);
            // 
            // bt_ch_down
            // 
            this.bt_ch_down.Location = new System.Drawing.Point(6, 187);
            this.bt_ch_down.Name = "bt_ch_down";
            this.bt_ch_down.Size = new System.Drawing.Size(116, 53);
            this.bt_ch_down.TabIndex = 12;
            this.bt_ch_down.Text = "DOWN";
            this.bt_ch_down.UseVisualStyleBackColor = true;
            this.bt_ch_down.Click += new System.EventHandler(this.bt_ch_down_Click);
            // 
            // tb_channel
            // 
            this.tb_channel.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tb_channel.Font = new System.Drawing.Font("휴먼모음T", 72F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.tb_channel.Location = new System.Drawing.Point(6, 63);
            this.tb_channel.Name = "tb_channel";
            this.tb_channel.Size = new System.Drawing.Size(245, 118);
            this.tb_channel.TabIndex = 11;
            this.tb_channel.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.tb_channel.KeyDown += new System.Windows.Forms.KeyEventHandler(this.tb_channel_keydown);
            // 
            // lb_rx_freq
            // 
            this.lb_rx_freq.AutoSize = true;
            this.lb_rx_freq.Font = new System.Drawing.Font("휴먼모음T", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.lb_rx_freq.Location = new System.Drawing.Point(4, 297);
            this.lb_rx_freq.Name = "lb_rx_freq";
            this.lb_rx_freq.Size = new System.Drawing.Size(61, 18);
            this.lb_rx_freq.TabIndex = 10;
            this.lb_rx_freq.Text = "0.0MHz";
            // 
            // lb_channetable
            // 
            this.lb_channetable.Font = new System.Drawing.Font("휴먼모음T", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.lb_channetable.FormattingEnabled = true;
            this.lb_channetable.ItemHeight = 18;
            this.lb_channetable.Location = new System.Drawing.Point(266, 63);
            this.lb_channetable.Name = "lb_channetable";
            this.lb_channetable.Size = new System.Drawing.Size(83, 238);
            this.lb_channetable.TabIndex = 9;
            this.lb_channetable.SelectedIndexChanged += new System.EventHandler(this.SelectedIndexChanged_lbChannel);
            // 
            // rb_intl_region
            // 
            this.rb_intl_region.AutoSize = true;
            this.rb_intl_region.Checked = true;
            this.rb_intl_region.Location = new System.Drawing.Point(12, 30);
            this.rb_intl_region.Name = "rb_intl_region";
            this.rb_intl_region.Size = new System.Drawing.Size(53, 17);
            this.rb_intl_region.TabIndex = 5;
            this.rb_intl_region.TabStop = true;
            this.rb_intl_region.Text = "INT\'L";
            this.rb_intl_region.UseVisualStyleBackColor = true;
            this.rb_intl_region.Click += new System.EventHandler(this.rb_region_checked_changed);
            // 
            // lb_sql_value
            // 
            this.lb_sql_value.AutoSize = true;
            this.lb_sql_value.Font = new System.Drawing.Font("휴먼모음T", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.lb_sql_value.ForeColor = System.Drawing.Color.Blue;
            this.lb_sql_value.Location = new System.Drawing.Point(254, 90);
            this.lb_sql_value.Name = "lb_sql_value";
            this.lb_sql_value.Size = new System.Drawing.Size(17, 18);
            this.lb_sql_value.TabIndex = 34;
            this.lb_sql_value.Text = "0";
            // 
            // lb_sql
            // 
            this.lb_sql.AutoSize = true;
            this.lb_sql.Location = new System.Drawing.Point(16, 92);
            this.lb_sql.Name = "lb_sql";
            this.lb_sql.Size = new System.Drawing.Size(58, 13);
            this.lb_sql.TabIndex = 33;
            this.lb_sql.Text = "SQUELCH";
            // 
            // tb_squelch
            // 
            this.tb_squelch.AutoSize = false;
            this.tb_squelch.BackColor = System.Drawing.SystemColors.Control;
            this.tb_squelch.LargeChange = 1;
            this.tb_squelch.Location = new System.Drawing.Point(76, 88);
            this.tb_squelch.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tb_squelch.Name = "tb_squelch";
            this.tb_squelch.Size = new System.Drawing.Size(172, 30);
            this.tb_squelch.TabIndex = 32;
            this.tb_squelch.Scroll += new System.EventHandler(this.main_sql_trackbar_scroll);
            // 
            // lb_vol_value
            // 
            this.lb_vol_value.AutoSize = true;
            this.lb_vol_value.Font = new System.Drawing.Font("휴먼모음T", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.lb_vol_value.ForeColor = System.Drawing.Color.Blue;
            this.lb_vol_value.Location = new System.Drawing.Point(254, 19);
            this.lb_vol_value.Name = "lb_vol_value";
            this.lb_vol_value.Size = new System.Drawing.Size(17, 18);
            this.lb_vol_value.TabIndex = 31;
            this.lb_vol_value.Text = "0";
            // 
            // lb_volume
            // 
            this.lb_volume.AutoSize = true;
            this.lb_volume.Location = new System.Drawing.Point(16, 23);
            this.lb_volume.Name = "lb_volume";
            this.lb_volume.Size = new System.Drawing.Size(66, 13);
            this.lb_volume.TabIndex = 30;
            this.lb_volume.Text = "MAIN VOL";
            // 
            // tb_rmt_vol
            // 
            this.tb_rmt_vol.AutoSize = false;
            this.tb_rmt_vol.BackColor = System.Drawing.SystemColors.Control;
            this.tb_rmt_vol.LargeChange = 1;
            this.tb_rmt_vol.Location = new System.Drawing.Point(76, 17);
            this.tb_rmt_vol.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tb_rmt_vol.Maximum = 100;
            this.tb_rmt_vol.Name = "tb_rmt_vol";
            this.tb_rmt_vol.Size = new System.Drawing.Size(172, 30);
            this.tb_rmt_vol.TabIndex = 29;
            this.tb_rmt_vol.Scroll += new System.EventHandler(this.main_vol_trackbar_scroll);
            // 
            // rb_dsc_mark
            // 
            this.rb_dsc_mark.AutoSize = true;
            this.rb_dsc_mark.Location = new System.Drawing.Point(63, 30);
            this.rb_dsc_mark.Name = "rb_dsc_mark";
            this.rb_dsc_mark.Size = new System.Drawing.Size(57, 17);
            this.rb_dsc_mark.TabIndex = 7;
            this.rb_dsc_mark.Text = "MARK";
            this.rb_dsc_mark.UseVisualStyleBackColor = true;
            this.rb_dsc_mark.Click += new System.EventHandler(this.rb_dsc_test_mode_clicked);
            // 
            // rb_dsc_space
            // 
            this.rb_dsc_space.AutoSize = true;
            this.rb_dsc_space.Location = new System.Drawing.Point(126, 30);
            this.rb_dsc_space.Name = "rb_dsc_space";
            this.rb_dsc_space.Size = new System.Drawing.Size(60, 17);
            this.rb_dsc_space.TabIndex = 8;
            this.rb_dsc_space.Text = "SPACE";
            this.rb_dsc_space.UseVisualStyleBackColor = true;
            this.rb_dsc_space.Click += new System.EventHandler(this.rb_dsc_test_mode_clicked);
            // 
            // rb_dsc_out_off
            // 
            this.rb_dsc_out_off.AutoSize = true;
            this.rb_dsc_out_off.Checked = true;
            this.rb_dsc_out_off.Location = new System.Drawing.Point(12, 30);
            this.rb_dsc_out_off.Name = "rb_dsc_out_off";
            this.rb_dsc_out_off.Size = new System.Drawing.Size(45, 17);
            this.rb_dsc_out_off.TabIndex = 6;
            this.rb_dsc_out_off.TabStop = true;
            this.rb_dsc_out_off.Text = "OFF";
            this.rb_dsc_out_off.UseVisualStyleBackColor = true;
            this.rb_dsc_out_off.Click += new System.EventHandler(this.rb_dsc_test_mode_clicked);
            // 
            // gb_knob
            // 
            this.gb_knob.Controls.Add(this.lb_hds_vol);
            this.gb_knob.Controls.Add(this.tb_rmt_hds_vol);
            this.gb_knob.Controls.Add(this.lb_hds_vol_value);
            this.gb_knob.Controls.Add(this.lb_sql_value);
            this.gb_knob.Controls.Add(this.lb_volume);
            this.gb_knob.Controls.Add(this.lb_sql);
            this.gb_knob.Controls.Add(this.tb_rmt_vol);
            this.gb_knob.Controls.Add(this.tb_squelch);
            this.gb_knob.Controls.Add(this.lb_vol_value);
            this.gb_knob.Font = new System.Drawing.Font("휴먼모음T", 9F);
            this.gb_knob.Location = new System.Drawing.Point(393, 45);
            this.gb_knob.Name = "gb_knob";
            this.gb_knob.Size = new System.Drawing.Size(286, 132);
            this.gb_knob.TabIndex = 2;
            this.gb_knob.TabStop = false;
            this.gb_knob.Text = "KNOB";
            // 
            // lb_hds_vol
            // 
            this.lb_hds_vol.AutoSize = true;
            this.lb_hds_vol.Location = new System.Drawing.Point(16, 57);
            this.lb_hds_vol.Name = "lb_hds_vol";
            this.lb_hds_vol.Size = new System.Drawing.Size(60, 13);
            this.lb_hds_vol.TabIndex = 36;
            this.lb_hds_vol.Text = "HDS VOL";
            // 
            // tb_rmt_hds_vol
            // 
            this.tb_rmt_hds_vol.AutoSize = false;
            this.tb_rmt_hds_vol.BackColor = System.Drawing.SystemColors.Control;
            this.tb_rmt_hds_vol.LargeChange = 1;
            this.tb_rmt_hds_vol.Location = new System.Drawing.Point(76, 51);
            this.tb_rmt_hds_vol.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tb_rmt_hds_vol.Maximum = 100;
            this.tb_rmt_hds_vol.Name = "tb_rmt_hds_vol";
            this.tb_rmt_hds_vol.Size = new System.Drawing.Size(172, 30);
            this.tb_rmt_hds_vol.TabIndex = 35;
            this.tb_rmt_hds_vol.Scroll += new System.EventHandler(this.main_hds_vol_trackbar_scroll);
            // 
            // lb_hds_vol_value
            // 
            this.lb_hds_vol_value.AutoSize = true;
            this.lb_hds_vol_value.Font = new System.Drawing.Font("휴먼모음T", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.lb_hds_vol_value.ForeColor = System.Drawing.Color.Blue;
            this.lb_hds_vol_value.Location = new System.Drawing.Point(254, 53);
            this.lb_hds_vol_value.Name = "lb_hds_vol_value";
            this.lb_hds_vol_value.Size = new System.Drawing.Size(17, 18);
            this.lb_hds_vol_value.TabIndex = 37;
            this.lb_hds_vol_value.Text = "0";
            // 
            // gb_power
            // 
            this.gb_power.Controls.Add(this.rb_25w);
            this.gb_power.Controls.Add(this.rb_1w);
            this.gb_power.Font = new System.Drawing.Font("휴먼모음T", 9F);
            this.gb_power.Location = new System.Drawing.Point(393, 183);
            this.gb_power.Name = "gb_power";
            this.gb_power.Size = new System.Drawing.Size(286, 59);
            this.gb_power.TabIndex = 35;
            this.gb_power.TabStop = false;
            this.gb_power.Text = "POWER";
            // 
            // rb_25w
            // 
            this.rb_25w.AutoSize = true;
            this.rb_25w.Location = new System.Drawing.Point(19, 21);
            this.rb_25w.Name = "rb_25w";
            this.rb_25w.Size = new System.Drawing.Size(49, 17);
            this.rb_25w.TabIndex = 16;
            this.rb_25w.Text = "25W";
            this.rb_25w.UseVisualStyleBackColor = true;
            this.rb_25w.Click += new System.EventHandler(this.rb_pwr_checked_changed);
            // 
            // rb_1w
            // 
            this.rb_1w.AutoSize = true;
            this.rb_1w.Location = new System.Drawing.Point(105, 21);
            this.rb_1w.Name = "rb_1w";
            this.rb_1w.Size = new System.Drawing.Size(42, 17);
            this.rb_1w.TabIndex = 17;
            this.rb_1w.Text = "1W";
            this.rb_1w.UseVisualStyleBackColor = true;
            this.rb_1w.Click += new System.EventHandler(this.rb_pwr_checked_changed);
            // 
            // gb_service
            // 
            this.gb_service.Controls.Add(this.cb_dsc_ber_test_mode);
            this.gb_service.Controls.Add(this.cb_dsc_signal_routing);
            this.gb_service.Font = new System.Drawing.Font("휴먼모음T", 9F);
            this.gb_service.Location = new System.Drawing.Point(700, 45);
            this.gb_service.Name = "gb_service";
            this.gb_service.Size = new System.Drawing.Size(252, 105);
            this.gb_service.TabIndex = 36;
            this.gb_service.TabStop = false;
            this.gb_service.Text = "SERVICE";
            // 
            // cb_dsc_ber_test_mode
            // 
            this.cb_dsc_ber_test_mode.AutoSize = true;
            this.cb_dsc_ber_test_mode.Location = new System.Drawing.Point(12, 64);
            this.cb_dsc_ber_test_mode.Name = "cb_dsc_ber_test_mode";
            this.cb_dsc_ber_test_mode.Size = new System.Drawing.Size(147, 17);
            this.cb_dsc_ber_test_mode.TabIndex = 3;
            this.cb_dsc_ber_test_mode.Text = "DSC BER TEST MODE";
            this.cb_dsc_ber_test_mode.UseVisualStyleBackColor = true;
            this.cb_dsc_ber_test_mode.CheckedChanged += new System.EventHandler(this.cb_service_CheckedChanged);
            // 
            // cb_dsc_signal_routing
            // 
            this.cb_dsc_signal_routing.AutoSize = true;
            this.cb_dsc_signal_routing.Location = new System.Drawing.Point(12, 31);
            this.cb_dsc_signal_routing.Name = "cb_dsc_signal_routing";
            this.cb_dsc_signal_routing.Size = new System.Drawing.Size(153, 17);
            this.cb_dsc_signal_routing.TabIndex = 2;
            this.cb_dsc_signal_routing.Text = "DSC SIGNAL ROUTING";
            this.cb_dsc_signal_routing.UseVisualStyleBackColor = true;
            this.cb_dsc_signal_routing.CheckedChanged += new System.EventHandler(this.cb_service_CheckedChanged);
            // 
            // gb_dsc_test_pattern
            // 
            this.gb_dsc_test_pattern.Controls.Add(this.rb_dsc_dot);
            this.gb_dsc_test_pattern.Controls.Add(this.rb_dsc_out_off);
            this.gb_dsc_test_pattern.Controls.Add(this.rb_dsc_space);
            this.gb_dsc_test_pattern.Controls.Add(this.rb_dsc_mark);
            this.gb_dsc_test_pattern.Font = new System.Drawing.Font("휴먼모음T", 9F);
            this.gb_dsc_test_pattern.Location = new System.Drawing.Point(700, 167);
            this.gb_dsc_test_pattern.Name = "gb_dsc_test_pattern";
            this.gb_dsc_test_pattern.Size = new System.Drawing.Size(252, 59);
            this.gb_dsc_test_pattern.TabIndex = 4;
            this.gb_dsc_test_pattern.TabStop = false;
            this.gb_dsc_test_pattern.Text = "DSC TEST PATTERN";
            // 
            // rb_dsc_dot
            // 
            this.rb_dsc_dot.AutoSize = true;
            this.rb_dsc_dot.Location = new System.Drawing.Point(192, 30);
            this.rb_dsc_dot.Name = "rb_dsc_dot";
            this.rb_dsc_dot.Size = new System.Drawing.Size(47, 17);
            this.rb_dsc_dot.TabIndex = 9;
            this.rb_dsc_dot.Text = "DOT";
            this.rb_dsc_dot.UseVisualStyleBackColor = true;
            this.rb_dsc_dot.Click += new System.EventHandler(this.rb_dsc_test_mode_clicked);
            // 
            // cb_PTT
            // 
            this.cb_PTT.Appearance = System.Windows.Forms.Appearance.Button;
            this.cb_PTT.Font = new System.Drawing.Font("굴림", 48F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.cb_PTT.Location = new System.Drawing.Point(393, 248);
            this.cb_PTT.Name = "cb_PTT";
            this.cb_PTT.Size = new System.Drawing.Size(286, 100);
            this.cb_PTT.TabIndex = 37;
            this.cb_PTT.Text = "PTT";
            this.cb_PTT.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.cb_PTT.UseVisualStyleBackColor = true;
            this.cb_PTT.CheckedChanged += new System.EventHandler(this.cb_PTT_CheckedChanged);
            // 
            // cb_DistBtn
            // 
            this.cb_DistBtn.Appearance = System.Windows.Forms.Appearance.Button;
            this.cb_DistBtn.BackColor = System.Drawing.SystemColors.Control;
            this.cb_DistBtn.Font = new System.Drawing.Font("굴림", 24F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(129)));
            this.cb_DistBtn.ForeColor = System.Drawing.SystemColors.ControlText;
            this.cb_DistBtn.Location = new System.Drawing.Point(393, 392);
            this.cb_DistBtn.Name = "cb_DistBtn";
            this.cb_DistBtn.Size = new System.Drawing.Size(286, 100);
            this.cb_DistBtn.TabIndex = 38;
            this.cb_DistBtn.Text = "DISTRESS";
            this.cb_DistBtn.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.cb_DistBtn.UseVisualStyleBackColor = false;
            this.cb_DistBtn.CheckedChanged += new System.EventHandler(this.cb_DistBtn_CheckedChanged);
            // 
            // Distress_timer
            // 
            this.Distress_timer.Interval = 1000;
            this.Distress_timer.Tick += new System.EventHandler(this.distress_timer_tick);
            // 
            // RemoteControlForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1002, 593);
            this.Controls.Add(this.cb_DistBtn);
            this.Controls.Add(this.cb_PTT);
            this.Controls.Add(this.gb_dsc_test_pattern);
            this.Controls.Add(this.gb_service);
            this.Controls.Add(this.gb_power);
            this.Controls.Add(this.gb_knob);
            this.Controls.Add(this.gb_rmt_serial);
            this.Controls.Add(this.toolStrip1);
            this.Name = "RemoteControlForm";
            this.TabText = "IV2500A Remote Control";
            this.Text = "IV2500A Remote Control";
            this.Load += new System.EventHandler(this.Load_RemoteControl);
            this.toolStrip1.ResumeLayout(false);
            this.toolStrip1.PerformLayout();
            this.gb_rmt_serial.ResumeLayout(false);
            this.gb_rmt_serial.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.tb_squelch)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tb_rmt_vol)).EndInit();
            this.gb_knob.ResumeLayout(false);
            this.gb_knob.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.tb_rmt_hds_vol)).EndInit();
            this.gb_power.ResumeLayout(false);
            this.gb_power.PerformLayout();
            this.gb_service.ResumeLayout(false);
            this.gb_service.PerformLayout();
            this.gb_dsc_test_pattern.ResumeLayout(false);
            this.gb_dsc_test_pattern.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.ToolStrip toolStrip1;
        private System.Windows.Forms.ToolStripLabel toolStripLabel_com;
        private System.Windows.Forms.ToolStripComboBox cb_comport;
        private System.Windows.Forms.ToolStripComboBox cb_baudrate;
        private System.Windows.Forms.ToolStripButton btn_ConnectIO;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.ToolStripLabel toolStripLabel2;
        private System.Windows.Forms.ToolStripTextBox ts_ip_addr;
        private System.Windows.Forms.ToolStripLabel toolStripLabel3;
        private System.Windows.Forms.ToolStripTextBox ts_port;
        private System.Windows.Forms.ToolStripButton ts_ip_connect;
        private System.Windows.Forms.GroupBox gb_rmt_serial;
        private System.Windows.Forms.RadioButton rb_dsc_mark;
        private System.Windows.Forms.RadioButton rb_dsc_space;
        private System.Windows.Forms.RadioButton rb_intl_region;
        private System.Windows.Forms.RadioButton rb_dsc_out_off;
        private System.Windows.Forms.ListBox lb_channetable;
        private System.Windows.Forms.Label lb_rx_freq;
        private System.Windows.Forms.TextBox tb_channel;
        private System.Windows.Forms.Button bt_ch_down;
        private System.Windows.Forms.Button bt_ch_up;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label lb_tx_freq;
        private System.Windows.Forms.Label lb_vol_value;
        private System.Windows.Forms.Label lb_volume;
        private System.Windows.Forms.TrackBar tb_rmt_vol;
        private System.Windows.Forms.Label lb_sql_value;
        private System.Windows.Forms.Label lb_sql;
        private System.Windows.Forms.TrackBar tb_squelch;
        private System.Windows.Forms.GroupBox gb_knob;
        private System.Windows.Forms.GroupBox gb_power;
        private System.Windows.Forms.RadioButton rb_25w;
        private System.Windows.Forms.RadioButton rb_1w;
        private System.Windows.Forms.GroupBox gb_service;
        private System.Windows.Forms.CheckBox cb_dsc_signal_routing;
        private System.Windows.Forms.GroupBox gb_dsc_test_pattern;
        private System.Windows.Forms.CheckBox cb_dsc_ber_test_mode;
        private System.Windows.Forms.RadioButton rb_dsc_dot;
        private System.Windows.Forms.CheckBox cb_PTT;
        private System.Windows.Forms.RadioButton rb_usa_region;
        private System.Windows.Forms.RadioButton rb_canada_region;
        private System.Windows.Forms.RadioButton rb_iww_region;
        private System.Windows.Forms.Label lb_hds_vol;
        private System.Windows.Forms.TrackBar tb_rmt_hds_vol;
        private System.Windows.Forms.Label lb_hds_vol_value;
        private System.Windows.Forms.CheckBox cb_DistBtn;
        private System.Windows.Forms.Timer Distress_timer;
    }
}