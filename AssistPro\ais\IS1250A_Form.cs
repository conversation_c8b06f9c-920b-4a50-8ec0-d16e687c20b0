using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO.Ports;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Controls.Primitives;
using System.Windows.Forms;
using System.Windows.Media;
using AssistPro.vhf;
using Microsoft.WindowsAPICodePack.Sensors;
using WeifenLuo.WinFormsUI;
using static AssistPro.ais.AISSentence;
using static AssistPro.SerialManager;

namespace AssistPro.ais
{
    public partial class IS1250A_Form : DockContent
    {
        public const int MAX_DATA_LEN = 4096;

        private List<CheckBox> msgCheckBoxList;
        private SerialManager m_SerialManager;
        private SerialPort m_SerialPort;
        private Thread m_serialThread;
        private SerialManager m_SerialManager2;
        private SerialPort m_SerialPort2;
        private Thread m_serialThread2;
        private AISSentence m_AisSentence;
        private AISSentence m_AisSentence2;
        private Dictionary<int, CheckBox> msgTypeCheckBoxMap;
        private volatile bool m_vdmAddEnabled = false;
        private int m_vdmElapsedSeconds = 0;
        private int m_vdmTargetSeconds = 0;
        private int m_CounterElapsedSeconds = 0;

        public class Packet_c
        {
            public int RxCnt = 0;
            public int TxCnt = 0;
            public byte[] TxBuffer = new byte[MAX_DATA_LEN];
            public byte[] RxBuffer = new byte[MAX_DATA_LEN];
            public int len = 0;
            public int sn = 0;
            public int cmd = 0;
            public int param = 0;
        }
        public Packet_c m_SerialBuffer = new Packet_c();
        public Packet_c m_SerialBuffer2 = new Packet_c();

        public IS1250A_Form()
        {
            InitializeComponent();
            m_AisSentence = new AISSentence(1, dgv_vdmlist, 
                                            IsMsgTypeChecked, IsMmsiFiltered, SendSentenceToSerial, 
                                            OnTxParamReceived, OnSentenceParamReceived);
            m_AisSentence2 = new AISSentence(2, dgv_vdmlist,
                                            IsMsgTypeChecked, IsMmsiFiltered, SendSentenceToSerial,
                                            OnTxParamReceived, OnSentenceParamReceived);

            // 메시지 체크박스 리스트 초기화
            msgCheckBoxList = new List<CheckBox>
            {
                cbMsg1, cbMsg4, cbMsg5, cbMsg6, cbMsg7, cbMsg8, cbMsg9, cbMsg10, cbMsg12,
                cbMsg14, cbMsg15, cbMsg16, cbMsg17, cbMsg18, cbMsg19, cbMsg20, cbMsg21,
                cbMsg22, cbMsg23, cbMsg24, cbMsg25, cbMsg26, cbMsg27
            };

            msgTypeCheckBoxMap = new Dictionary<int, CheckBox>
            {
                { 1, cbMsg1 }, { 4, cbMsg4 }, { 5, cbMsg5 }, { 6, cbMsg6 }, { 7, cbMsg7 },
                { 8, cbMsg8 }, { 9, cbMsg9 }, { 10, cbMsg10 }, { 12, cbMsg12 }, { 14, cbMsg14 },
                { 15, cbMsg15 }, { 16, cbMsg16 }, { 17, cbMsg17 }, { 18, cbMsg18 }, { 19, cbMsg19 },
                { 20, cbMsg20 }, { 21, cbMsg21 }, { 22, cbMsg22 }, { 23, cbMsg23 }, { 24, cbMsg24 },
                { 25, cbMsg25 }, { 26, cbMsg26 }, { 27, cbMsg27 }
            };

            m_VdmTimer.Tick += timer_vdm_Tick;
            m_VdmTimer.Interval = 1000;

            rb_TxFreq70.CheckedChanged += Frequency_Power_RadioButton_CheckedChanged;
            rb_TxFreq75.CheckedChanged += Frequency_Power_RadioButton_CheckedChanged;
            rb_TxFreq76.CheckedChanged += Frequency_Power_RadioButton_CheckedChanged;
            rb_TxFreq2086.CheckedChanged += Frequency_Power_RadioButton_CheckedChanged;
            rb_TxFreq2087.CheckedChanged += Frequency_Power_RadioButton_CheckedChanged;
            rb_TxFreq2088.CheckedChanged += Frequency_Power_RadioButton_CheckedChanged;
            rb_TxPowerHigh.CheckedChanged += Frequency_Power_RadioButton_CheckedChanged;
            rb_TxPowerLow.CheckedChanged += Frequency_Power_RadioButton_CheckedChanged;
        }

        private void OnTxParamReceived(TxParamEventArgs e)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new Action(() => OnTxParamReceived(e)));
                return;
            }

            switch (e.Cmd)
            {
                case TM_MODE.TM_GET_TX_PARAM:
                    System.Diagnostics.Debug.WriteLine($"[TM_GET_TX_PARAM] Freq: {e.Param1}, Param2: {e.Param2}");

                    switch (e.Param1)
                    {
                        case 156525000:
                            rb_TxFreq70.Checked = true;
                            break;
                        case 156775000:
                            rb_TxFreq75.Checked = true;
                            break;
                        case 156825000:
                            rb_TxFreq76.Checked = true;
                            break;
                        case 161925000:
                            rb_TxFreq2086.Checked = true;
                            break;
                        case 161975000:
                            rb_TxFreq2087.Checked = true;
                            break;
                        case 162025000:
                            rb_TxFreq2088.Checked = true;
                            break;
                    }

                    if (e.Param2 == 0)
                        rb_TxPowerHigh.Checked = true;
                    else
                        rb_TxPowerLow.Checked = true;
                    break;
                case TM_MODE.TM_GET_TX_POWER:
                    // 파워 적용
                    tb_DiagPowerSet.Text = e.Param1.ToString();
                    break;
                case TM_MODE.TM_GET_TX_FREQ_OFFSET:
                    // 주파수 오프셋 적용
                    tb_DiagBaseFreq.Text = e.Param1.ToString();
                    break;
                case TM_MODE.TM_GET_TX_DC_OFFSET:
                    // DC 오프셋 적용
                    tb_DiagDCCenter.Text = e.Param1.ToString();
                    tb_DiagDCShift.Text = e.Param2.ToString();
                    break;
                case TM_MODE.TM_GET_VSWRLIMIT:
                    tb_DiagVswr.Text = e.Param1.ToString();
                    break;
            }
        }

        private void OnSentenceParamReceived(SentenceParamEventArgs e)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new Action(() => OnSentenceParamReceived(e)));
                return;
            }

            switch (e.Cmd)
            {
                case SENTENCE_TYPE.ST_SSD:
                    var parts = e.Param1.Split(',');
                    if (parts.Length >= 8)
                    {
                        try
                        {
                            string CallSign = parts[1];
                            string ShipName = parts[2];

                            int wA = Convert.ToInt32(parts[3]);
                            int wB = Convert.ToInt32(parts[4]);
                            int wC = Convert.ToInt32(parts[5]);
                            int wD = Convert.ToInt32(parts[6]);

                            int DTE = Convert.ToInt32(parts[7]);

                            string AntType = parts[8];

                            tb_StaticCallSign.Text = CallSign;
                            tb_StaticShipName.Text = ShipName;

                            if (AntType == "AI")
                            {
                                tb_AntInternalA.Text = wA.ToString();
                                tb_AntInternalB.Text = wB.ToString();
                                tb_AntInternalC.Text = wC.ToString();
                                tb_AntInternalD.Text = wD.ToString();
                            }
                            else if (AntType == "GN")
                            {
                                tb_AntExternalA.Text = wA.ToString();
                                tb_AntExternalB.Text = wB.ToString();
                                tb_AntExternalC.Text = wC.ToString();
                                tb_AntExternalD.Text = wD.ToString();
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine("VSD 파싱 오류: " + ex.Message);
                        }
                    }
                    break;

                case SENTENCE_TYPE.ST_VSD:
                    parts = e.Param1.Split(',');
                    if (parts.Length >= 8)
                    {
                        try
                        {
                            int ShipType = Convert.ToInt32(parts[1], 10);
                            double Draught = Convert.ToDouble(parts[2]);
                            int Person = Convert.ToInt32(parts[3], 10);
                            string Destination = parts[4];

                            int.TryParse(parts[5], out int ETA_Hour);
                            int hour = ETA_Hour / 10000;
                            int minute = (ETA_Hour / 100) % 100;
                            int second = ETA_Hour % 100;

                            int day = Convert.ToInt32(parts[6], 10);
                            int month = Convert.ToInt32(parts[7], 10);

                            int NavStatus = Convert.ToInt32(parts[8], 10);
                            int Manoeuvre = Convert.ToInt32(parts[9], 10);

                            m_AisSentence.m_OwnShipInfo.SetShipCargoType(ShipType);
                            m_AisSentence.m_OwnShipInfo.SetDraught((float)Draught);
                            m_AisSentence.m_OwnShipInfo.SetPersonOnBoard(Person);
                            m_AisSentence.m_OwnShipInfo.SetDestination(Destination);

                            m_AisSentence.m_OwnShipInfo.SetEstHour(hour);
                            m_AisSentence.m_OwnShipInfo.SetEstMinute(minute);
                            m_AisSentence.m_OwnShipInfo.SetEstDay(day);
                            m_AisSentence.m_OwnShipInfo.SetEstMonth(month);
                            m_AisSentence.m_OwnShipInfo.SetNavStatus(NavStatus);

                            int firstDigit = ShipType / 10;
                            int secondDigit = ShipType % 10;

                            switch (firstDigit)
                            {
                                case 0:
                                    cb_VoyageCargo.Items.Clear();
                                    cb_VoyageCargo.Enabled = false;
                                    cb_StaticShipType.SelectedIndex = -1;
                                    break;
                                case 3:
                                    cb_VoyageCargo.Enabled = true;
                                    cb_VoyageCargo.Items.Clear();
                                    cb_VoyageCargo.Items.Add("0-Fishing");
                                    cb_VoyageCargo.Items.Add("1-Towing");
                                    cb_VoyageCargo.Items.Add("2-Towing and tow exceeds 200m");
                                    cb_VoyageCargo.Items.Add("3-Engaged in dredging or underwater operations");
                                    cb_VoyageCargo.Items.Add("4-Engaged in diving operations");
                                    cb_VoyageCargo.Items.Add("5-Engaged in military operations");
                                    cb_VoyageCargo.Items.Add("6-Sailing");
                                    cb_VoyageCargo.Items.Add("7-Pleasure craft");
                                    cb_VoyageCargo.Items.Add("8-Reserved for future use");
                                    cb_VoyageCargo.Items.Add("9-Reserved for future use");
                                    cb_VoyageCargo.SelectedIndex = secondDigit;
                                    break;
                                case 5:
                                    cb_VoyageCargo.Items.Clear();
                                    cb_VoyageCargo.Enabled = false;
                                    cb_StaticShipType.SelectedIndex = 5+secondDigit;
                                    break;
                                default:
                                    cb_VoyageCargo.Enabled = true;
                                    cb_VoyageCargo.Items.Clear();
                                    cb_VoyageCargo.Items.Add("0-All ships of this type");
                                    cb_VoyageCargo.Items.Add("1-Carrying DG, HS, or MP(X)");
                                    cb_VoyageCargo.Items.Add("2-Carrying DG, HS, or MP(Y)");
                                    cb_VoyageCargo.Items.Add("3-Carrying DG, HS, or MP(Z)");
                                    cb_VoyageCargo.Items.Add("4-Carrying DG, HS, or MP(OS)");
                                    cb_VoyageCargo.Items.Add("5-Reserved for future use");
                                    cb_VoyageCargo.Items.Add("6-Reserved for future use");
                                    cb_VoyageCargo.Items.Add("7-Reserved for future use");
                                    cb_VoyageCargo.Items.Add("8-Reserved for future use");
                                    cb_VoyageCargo.Items.Add("9-No additional information");
                                    cb_VoyageCargo.SelectedIndex = secondDigit;
                                    break;
                            }

                            cb_StaticShipType.SelectedIndex = (ShipType/10);                            tb_VoyageDraught.Text = Draught.ToString();
                            tb_VoyagePerson.Text = Person.ToString();
                            tb_VoyageDest.Text = Destination;

                            if (month > 0 && month <= 12 && day > 0 && day <= DateTime.DaysInMonth(DateTime.Now.Year, month))
                            {
                                cb_VoyageMonth.SelectedIndex = month-1;
                                cb_VoyageDay.SelectedIndex = day-1;
                            }
                            else
                            {
                                cb_VoyageMonth.SelectedIndex = -1;
                                cb_VoyageDay.SelectedIndex = -1;
                            }

                            if (hour >= 0 && hour < 24 && minute >= 0 && minute < 60)
                            {
                                cb_VoyageHour.SelectedIndex = hour;
                                cb_VoyageMinute.SelectedIndex = minute;
                            }
                            else
                            {
                                cb_VoyageHour.SelectedIndex = -1;
                                cb_VoyageMinute.SelectedIndex = -1;
                            }

                            cb_VoyageNavStatus.SelectedIndex = NavStatus;
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine("VSD 파싱 오류: " + ex.Message);
                        }
                    }
                    break;

                case SENTENCE_TYPE.ST_106:
                    tb_StaticMMSI.Text = string.Format("{0:D9}", e.Param2);
                    break;

                case SENTENCE_TYPE.ST_107:
                    tb_StaticIMO.Text = string.Format("{0:D10}", e.Param2);
                    break;
            }
        }

        private void timer_vdm_Tick(object sender, EventArgs e)
        {
            m_vdmElapsedSeconds++;
            labelTimeValue.Text = string.Format("{0:D2}:{1:D2}:{2:D2}", m_vdmElapsedSeconds / 3600, m_vdmElapsedSeconds / 60, m_vdmElapsedSeconds % 60);

            if (m_vdmTargetSeconds != 0 && m_vdmElapsedSeconds >= m_vdmTargetSeconds)
            {
                btn_VdmStart.Text = "START";
                //m_AisSentence.ClearMsgCount();
                m_vdmAddEnabled = false;
                m_VdmTimer.Stop();
            }
        }

        private bool IsMmsiFiltered(string userId)
        {
            // cbFilterMMSIAll가 체크되어 있으면 필터링하지 않음
            if (cbFilterMMSIAll.Checked)
                return true;

            // tbFilterMMSI1~5의 값과 비교 (공백/빈값은 무시)
            string[] filterMmsis = new string[]
            {
                tbFilterMMSI1.Text.Trim(),
                tbFilterMMSI2.Text.Trim(),
                tbFilterMMSI3.Text.Trim(),
                tbFilterMMSI4.Text.Trim(),
                tbFilterMMSI5.Text.Trim()
            };

            foreach (var mmsi in filterMmsis)
            {
                if (!string.IsNullOrEmpty(mmsi) && mmsi == userId)
                    return true;
            }
            return false;
        }

        public bool IsMsgTypeChecked(int msgType)
        {
            if (msgTypeCheckBoxMap.TryGetValue(msgType, out var cb))
                return cb.Checked;
            return false;
        }

        private void SendSentenceToSerial(string sentence)
        {
            if (m_SerialPort != null && m_SerialPort.IsOpen)
            {
                try
                {
                    m_SerialPort.Write(sentence);
                }
                catch (Exception ex)
                {
                    MessageBox.Show("Serial 송신 오류: " + ex.Message);
                }
            }
        }

        private void TabPageOnOff(bool isOn)
        {
            tabPage1.Enabled = isOn;
            tabPage2.Enabled = isOn;
            tabPage3.Enabled = isOn;
            tabPage4.Enabled = isOn;
        }

        private void Load_AisSimulator(object sender, EventArgs e)
        {
            foreach (string name in SerialPort.GetPortNames())
            {
                cbCom.Items.Add(name);
                cbCom2.Items.Add(name);
            }

            cbBaud.Items.Add(4800);
            cbBaud.Items.Add(9600);
            cbBaud.Items.Add(38400);
            cbBaud.Items.Add(115200);

            cbBaud2.Items.Add(4800);
            cbBaud2.Items.Add(9600);
            cbBaud2.Items.Add(38400);
            cbBaud2.Items.Add(115200);
        }

        private void serialPort_DataReceived(object sender, SerialDataReceivedEventArgs e)
        {
            this.Invoke(new EventHandler(SerialData_Received));
        }

        private void SerialData_Received(object s, EventArgs e)
        {
            m_SerialManager.Enqueue();
        }

        private void serialPort_DataReceived2(object sender, SerialDataReceivedEventArgs e)
        {
            this.Invoke(new EventHandler(SerialData_Received2));
        }

        private void SerialData_Received2(object s, EventArgs e)
        {
            m_SerialManager2.Enqueue();
        }

        private void SerialDataThread()
        {
            byte RxData = 0;

            while (true)
            {
                while (m_SerialManager.Dequeue(ref RxData) != BUFF_STATUS.BUFF_NULL_DATA)
                {
                    if (RxData != 0x0A)
                    {
                        if (m_SerialBuffer.TxCnt < MAX_DATA_LEN)
                            m_SerialBuffer.RxBuffer[m_SerialBuffer.TxCnt++] = RxData;
                        else
                        {
                            m_SerialBuffer.TxCnt = 0;
                            Array.Clear(m_SerialBuffer.RxBuffer, 0, MAX_DATA_LEN);
                            m_SerialBuffer.RxBuffer[m_SerialBuffer.TxCnt++] = RxData;
                        }
                    }
                    else
                    {
                        m_SerialBuffer.RxBuffer[m_SerialBuffer.TxCnt++] = RxData;
                        string sentence = Encoding.Default.GetString(m_SerialBuffer.RxBuffer, 0, m_SerialBuffer.TxCnt);
                        Invoke(new Action(() =>
                        {
                            tb_logging.AppendText(sentence);
                        }));

                        // Call Sententence process function
                        m_AisSentence.SentenceProcess(sentence, 1, m_vdmAddEnabled);

                        m_SerialBuffer.TxCnt = 0;
                        Array.Clear(m_SerialBuffer.RxBuffer, 0, MAX_DATA_LEN);
                    }
                }
                Thread.Sleep(1);
            }
        }

        private void SerialDataThread2()
        {
            byte RxData = 0;

            while (true)
            {
                while (m_SerialManager2.Dequeue(ref RxData) != BUFF_STATUS.BUFF_NULL_DATA)
                {
                    if (RxData != 0x0A)
                    {
                        if (m_SerialBuffer2.TxCnt < MAX_DATA_LEN)
                            m_SerialBuffer2.RxBuffer[m_SerialBuffer2.TxCnt++] = RxData;
                        else
                        {
                            m_SerialBuffer2.TxCnt = 0;
                            Array.Clear(m_SerialBuffer2.RxBuffer, 0, MAX_DATA_LEN);
                            m_SerialBuffer2.RxBuffer[m_SerialBuffer2.TxCnt++] = RxData;
                        }
                    }
                    else
                    {
                        m_SerialBuffer2.RxBuffer[m_SerialBuffer2.TxCnt++] = RxData;
                        string sentence = Encoding.Default.GetString(m_SerialBuffer2.RxBuffer, 0, m_SerialBuffer2.TxCnt);
                        Invoke(new Action(() =>
                        {
                            tb_logging2.AppendText(sentence);
                        }));

                        // Call Sententence process function
                        m_AisSentence2.SentenceProcess(sentence, 2, m_vdmAddEnabled);

                        m_SerialBuffer2.TxCnt = 0;
                        Array.Clear(m_SerialBuffer2.RxBuffer, 0, MAX_DATA_LEN);
                    }
                }
                Thread.Sleep(1);
            }
        }

        private void btnConnect_Click(object sender, EventArgs e)
        {
            try
            {
                if (cbCom.Text == "")
                {
                    MessageBox.Show(" You need select port. ");
                    return;
                }

                if (cbBaud.Text == "")
                {
                    MessageBox.Show(" You need select baud rate ");
                    return;
                }

                if (cbCom.Enabled)
                {
                    m_SerialManager = new SerialManager(cbCom.Text, int.Parse(cbBaud.Text));
                    m_SerialPort = m_SerialManager.GetInstance();
                    m_SerialPort.DataReceived += serialPort_DataReceived;
                    m_SerialPort.Open();

                    btnConnect.Text = "DISCONNECT";
                    cbCom.Enabled = false;
                    cbBaud.Enabled = false;
                    btnConnect.BackColor = System.Drawing.Color.PaleGreen;

                    m_serialThread = new Thread(SerialDataThread);
                    m_serialThread.Priority = ThreadPriority.Highest;
                    m_serialThread.Start();

                    m_CounterTimer.Stop();
                    m_CounterTimer.Start();
                }
                else
                {
                    m_SerialPort.Close();
                    btnConnect.Text = "CONNECT";
                    cbCom.Enabled = true;
                    cbBaud.Enabled = true;
                    btnConnect.BackColor = System.Drawing.Color.Silver;

                    m_CounterTimer.Stop();

                    m_serialThread.Abort();
                }
            }
            catch (Exception ea)
            {
                MessageBox.Show(ea.Message);
                cbCom.Enabled = true;
                cbBaud.Enabled = true;
                btnConnect.BackColor = System.Drawing.Color.Silver;
                btnConnect.Text = "CONNECT";
            }
        }

        private void btnConnect2_Click(object sender, EventArgs e)
        {
            try
            {
                if (cbCom2.Text == "")
                {
                    MessageBox.Show(" You need select port. ");
                    return;
                }

                if (cbBaud2.Text == "")
                {
                    MessageBox.Show(" You need select baud rate ");
                    return;
                }

                if (cbCom2.Enabled)
                {
                    m_SerialManager2 = new SerialManager(cbCom2.Text, int.Parse(cbBaud2.Text));
                    m_SerialPort2 = m_SerialManager2.GetInstance();
                    m_SerialPort2.DataReceived += serialPort_DataReceived2;
                    m_SerialPort2.Open();

                    btnConnect2.Text = "DISCONNECT";
                    cbCom2.Enabled = false;
                    cbBaud2.Enabled = false;
                    btnConnect2.BackColor = System.Drawing.Color.PaleGreen;

                    m_serialThread2 = new Thread(SerialDataThread2);
                    m_serialThread2.Priority = ThreadPriority.Highest;
                    m_serialThread2.Start();

                    m_CounterTimer.Stop();
                    m_CounterTimer.Start();
                }
                else
                {
                    m_SerialPort2.Close();
                    btnConnect2.Text = "CONNECT";
                    cbCom2.Enabled = true;
                    cbBaud2.Enabled = true;
                    btnConnect2.BackColor = System.Drawing.Color.Silver;
                    m_serialThread2.Abort();
                }
            }
            catch (Exception ea)
            {
                MessageBox.Show(ea.Message);
                cbCom2.Enabled = true;
                cbBaud2.Enabled = true;
                btnConnect2.BackColor = System.Drawing.Color.Silver;
                btnConnect2.Text = "CONNECT";
            }
        }

        private void cbMsgAll_CheckedChanged(object sender, EventArgs e)
        {
            bool isChecked = cbMsgAll.Checked;
            foreach (var cb in msgCheckBoxList)
            {
                cb.Checked = isChecked;
                if (isChecked)
                    cb.Enabled = false;
                else
                    cb.Enabled = true;
            }
        }

        private void dgv_vdmlist_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                var row = dgv_vdmlist.Rows[e.RowIndex];
                string message = row.Cells["DvgBits"].Value?.ToString() ?? "";

                var vdmDetailForm = new AisVdmDetailForm(message);
                vdmDetailForm.ShowDialog();
            }
        }

        private void btn_StaticLoad_Click(object sender, EventArgs e)
        {
            // Request MMSI
            m_AisSentence.SendAISSentence(SENTENCE_TYPE.ST_INL, SENTENCE_TYPE.ST_AIQ, SENTENCE_TYPE.ST_106);
            // Request IMO
            m_AisSentence.SendAISSentence(SENTENCE_TYPE.ST_INL, SENTENCE_TYPE.ST_AIQ, SENTENCE_TYPE.ST_107);
            // Request Static data
            m_AisSentence.SendAISSentence(SENTENCE_TYPE.ST_AIQ, SENTENCE_TYPE.ST_SSD);
        }

        private void btn_StaticSave_Click(object sender, EventArgs e)
        {
            // Send Master Password for EPV
            m_AisSentence.SendAISSentence(SENTENCE_TYPE.ST_SPW, SENTENCE_TYPE.ST_EPV, 0, (int)m_AisSentence.m_OwnShipInfo.GetMMSI(), (int)PW_LEVEL.MASTER);

            // Set MMSI if it has changed
            if (m_AisSentence.m_OwnShipInfo.GetMMSI() != Convert.ToUInt32(tb_StaticMMSI.Text))
            {
                m_AisSentence.SendAISSentence(SENTENCE_TYPE.ST_EPV, SENTENCE_TYPE.ST_106, 0, (int)Convert.ToUInt32(tb_StaticMMSI.Text));
            }

            // Set IMO if it has changed
            if (m_AisSentence.m_OwnShipInfo.GetIMO() != Convert.ToUInt32(tb_StaticIMO.Text))
            {
                m_AisSentence.SendAISSentence(SENTENCE_TYPE.ST_EPV, SENTENCE_TYPE.ST_107, 0, (int)Convert.ToUInt32(tb_StaticIMO.Text));
            }


            // Send Master Password for SSD
            m_AisSentence.SendAISSentence(SENTENCE_TYPE.ST_SPW, SENTENCE_TYPE.ST_SSD, 0, (int)m_AisSentence.m_OwnShipInfo.GetMMSI(), (int)PW_LEVEL.MASTER);

            // Set other static data
            m_AisSentence.m_OwnShipInfo.SetShipName(tb_StaticShipName.Text);
            m_AisSentence.m_OwnShipInfo.SetCallSign(tb_StaticCallSign.Text);
            m_AisSentence.m_OwnShipInfo.SetIntAntPos(Convert.ToInt32(tb_AntInternalA.Text), Convert.ToInt32(tb_AntInternalB.Text), Convert.ToInt32(tb_AntInternalC.Text), Convert.ToInt32(tb_AntInternalD.Text));
            m_AisSentence.m_OwnShipInfo.SetExtAntPos(Convert.ToInt32(tb_AntExternalA.Text), Convert.ToInt32(tb_AntExternalB.Text), Convert.ToInt32(tb_AntExternalC.Text), Convert.ToInt32(tb_AntExternalD.Text));
            m_AisSentence.SendAISSentence(SENTENCE_TYPE.ST_SSD, 0, 0, 0);
            m_AisSentence.SendAISSentence(SENTENCE_TYPE.ST_SSD, 0, 0, 1);
        }

        private void btn_LoggingClear_Click(object sender, EventArgs e)
        {
            tb_logging.Clear();
        }

        private void btn_LoggingSave_Click(object sender, EventArgs e)
        {
            using (SaveFileDialog saveFileDialog = new SaveFileDialog())
            {
                saveFileDialog.Filter = "텍스트 파일 (*.txt)|*.txt|모든 파일 (*.*)|*.*";
                saveFileDialog.Title = "로그 저장";
                saveFileDialog.FileName = $"AIS_Log_{DateTime.Now:yyyyMMdd_HHmmss}.txt";

                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        System.IO.File.WriteAllText(saveFileDialog.FileName, tb_logging.Text, Encoding.UTF8);
                        MessageBox.Show("로그가 성공적으로 저장되었습니다.", "저장 완료", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show("로그 저장 중 오류가 발생했습니다.\n" + ex.Message, "오류", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void btn_SettingLoad_Click(object sender, EventArgs e)
        {

        }

        private void btn_SettingSave_Click(object sender, EventArgs e)
        {

        }

        private void btn_TxTestStart_Click(object sender, EventArgs e)
        {
            int txPattern = cb_DiagPattern.SelectedIndex;

            if (txPattern < 0)
            {
                MessageBox.Show("Please select a transmission pattern.");
                return;
            }

            txPattern += 2; // Adjust index to match TM_MODE enum (2-based)
            m_AisSentence.SendTxParam(TM_MODE.TM_MODE_START, txPattern);
        }

        private void btnTxTestStop_Click(object sender, EventArgs e)
        {
            m_AisSentence.SendTxParam(TM_MODE.TM_MODE_STOP);
        }

        private void btn_RxTestStart_Click(object sender, EventArgs e)
        {
        }

        private void btn_RxTestStop_Click(object sender, EventArgs e)
        {
        }

        private void btn_VdmStart_Click(object sender, EventArgs e)
        {
            if (m_vdmAddEnabled == false)
            {
                btn_VdmStart.Text = "STOP";

                m_AisSentence.ClearMsgCount();
                m_AisSentence2.ClearMsgCount();
                m_vdmAddEnabled = true;

                m_vdmElapsedSeconds = 0;
                labelTimeValue.Text = "00:00:00";

                int seconds = 0;
                if (rbTimer1min.Checked)
                    seconds = 60;
                else if (rbTimer3min.Checked)
                    seconds = 180;
                else if (rbTimer5min.Checked)
                    seconds = 300;

                m_vdmTargetSeconds = seconds;

                //if (seconds > 0)
                {
                    m_VdmTimer.Interval = 1000;
                    m_VdmTimer.Start();
                }
            }
            else
            {
                btn_VdmStart.Text = "START";
                m_AisSentence.ClearMsgCount();
                m_AisSentence2.ClearMsgCount();
                m_vdmAddEnabled = false;
                m_VdmTimer.Stop();
                //labelTimeValue.Text = "00:00:00";
            }
        }

        private void btn_VdmClearList_Click(object sender, EventArgs e)
        {
            dgv_vdmlist.Rows.Clear();
            m_AisSentence.ClearMsgCount();
            m_AisSentence2.ClearMsgCount();
        }

        private void cbFilterMMSIAll_CheckedChanged(object sender, EventArgs e)
        {
            if (cbFilterMMSIAll.Checked)
            {
                tbFilterMMSI1.Enabled = false;
                tbFilterMMSI2.Enabled = false;
                tbFilterMMSI3.Enabled = false;
                tbFilterMMSI4.Enabled = false;
                tbFilterMMSI5.Enabled = false;
            }
            else
            {
                tbFilterMMSI1.Enabled = true;
                tbFilterMMSI2.Enabled = true;
                tbFilterMMSI3.Enabled = true;
                tbFilterMMSI4.Enabled = true;
                tbFilterMMSI5.Enabled = true;
            }
        }

        private void rb_RFTXON_CheckedChanged(object sender, EventArgs e)
        {
            if (rb_RFTXON.Checked)
            {
                gb_TxTest.Enabled = true;
                m_AisSentence.SendRfTestEnable(1);
            }
        }

        private void rb_RFRXON_CheckedChanged(object sender, EventArgs e)
        {
            if (rb_RFRXON.Checked)
            {
                gb_RxTest.Enabled = true;
                m_AisSentence.SendRfTestEnable(2);
            }
        }

        private void rb_RFTestOff_CheckedChanged(object sender, EventArgs e)
        {
            if (rb_RFTestOff.Checked)
            {
                gb_TxTest.Enabled = false;
                gb_RxTest.Enabled = false;
                m_AisSentence.SendRfTestEnable(0);
            }
        }

        private void btn_TxTestSet_Click(object sender, EventArgs e)
        {
            int freq = 0;
            int power = 0;
            int powerOffset = 0;
            int baseFreq = 0;
            int vswrLimit = 0;
            int dcOffset = 0; // DC 오프셋
            int dcShift = 0; // DC 시프트

            if (rb_TxFreq60.Checked)
                freq = 156025000;
            else if (rb_TxFreq70.Checked)
                freq = 156525000;
            else if (rb_TxFreq75.Checked)
                freq = 156775000;
            else if (rb_TxFreq76.Checked)
                freq = 156825000;
            else if (rb_TxFreq2086.Checked)
                freq = 161925000;
            else if (rb_TxFreq2087.Checked)
                freq = 161975000;
            else if (rb_TxFreq2088.Checked)
                freq = 162025000;

            if (rb_TxPowerHigh.Checked)
                power = 0; // High Power
            else if (rb_TxPowerLow.Checked)
                power = 1; // Low Power

            freq = ((freq / 1000) * 80 / 1000);
            m_AisSentence.SendTxParam(TM_MODE.TM_SET_TX_PARAM, freq, power);

            powerOffset = int.Parse(tb_DiagPowerSet.Text);
            m_AisSentence.SendTxParam(TM_MODE.TM_SET_TX_POWER, powerOffset);

            baseFreq = int.Parse(tb_DiagBaseFreq.Text);
            m_AisSentence.SendTxParam(TM_MODE.TM_SET_TX_FREQ_OFFSET, baseFreq);

            dcOffset = int.Parse(tb_DiagDCCenter.Text);
            dcShift = int.Parse(tb_DiagDCShift.Text);
            m_AisSentence.SendTxParam(TM_MODE.TM_SET_TX_DC_OFFSET, dcOffset, dcShift);

            vswrLimit = int.Parse(tb_DiagVswr.Text);
            m_AisSentence.SendTxParam(TM_MODE.TM_SET_VSWRLIMIT, vswrLimit);
        }

        private void Frequency_Power_RadioButton_CheckedChanged(object sender, EventArgs e)
        {
            if (!(sender is RadioButton rb) || !rb.Checked)
                return;

            int freq = 0;
            int power = 0;

            if (rb_TxFreq70.Checked)
                freq = 156525000;
            else if (rb_TxFreq75.Checked)
                freq = 156775000;
            else if (rb_TxFreq76.Checked)
                freq = 156825000;
            else if (rb_TxFreq2086.Checked)
                freq = 161925000;
            else if (rb_TxFreq2087.Checked)
                freq = 161975000;
            else if (rb_TxFreq2088.Checked)
                freq = 162025000;

            if (rb_TxPowerHigh.Checked)
                power = 0; // High Power
            else if (rb_TxPowerLow.Checked)
                power = 1; // Low Power

            freq = ((freq / 1000) * 80 / 1000);
            m_AisSentence.SendTxParam(TM_MODE.TM_SET_TX_PARAM, freq, power);

            //m_AisSentence.SendTxParam(TM_MODE.TM_GET_TX_POWER);
        }

        private void btn_VoyageLoad_Click(object sender, EventArgs e)
        {
            m_AisSentence.SendAISSentence(SENTENCE_TYPE.ST_AIQ, SENTENCE_TYPE.ST_VSD);
        }

        private void btn_VoyageSave_Click(object sender, EventArgs e)
        {
            int cargoType = 0;

            if (cb_StaticShipType.SelectedIndex < 0)
            {
                MessageBox.Show("Please select a ship type.");
                return;
            }

            switch (cb_StaticShipType.SelectedIndex)
            {
                case 0:
                    cargoType = 0;
                    break;
                case 1:
                case 2:
                case 3:
                case 4:
                    if (cb_VoyageCargo.SelectedIndex < 0)
                    {
                        MessageBox.Show("Please select a cargo type.");
                        return;
                    }
                    cargoType = cb_StaticShipType.SelectedIndex * 10 + cb_VoyageCargo.SelectedIndex;
                    break;
                case 5:
                case 6:
                case 7:
                case 8:
                case 9:
                case 10:
                case 11:
                case 12:
                case 13:
                case 14:
                    cargoType = 5 * 10 + (cb_StaticShipType.SelectedIndex - 5);
                    break;
                case 15:
                case 16:
                case 17:
                case 18:
                    if (cb_VoyageCargo.SelectedIndex < 0)
                    {
                        MessageBox.Show("Please select a cargo type.");
                        return;
                    }
                    cargoType = (cb_StaticShipType.SelectedIndex - 9) * 10 + cb_VoyageCargo.SelectedIndex;
                    break;
                default:
                    return;
            }

            m_AisSentence.m_OwnShipInfo.SetShipCargoType(cargoType);
            if (tb_VoyageDraught.Text.Length > 0)
            {
                m_AisSentence.m_OwnShipInfo.SetDraught((float)Convert.ToDouble(tb_VoyageDraught.Text));
            }

            if (tb_VoyagePerson.Text.Length > 0)
            {
                m_AisSentence.m_OwnShipInfo.SetPersonOnBoard(Convert.ToInt32(tb_VoyagePerson.Text));
            }

            if (tb_VoyageDest.Text.Length > 0)
            {
                m_AisSentence.m_OwnShipInfo.SetDestination(tb_VoyageDest.Text);
            }

            if (cb_VoyageHour.SelectedIndex < 0)
            {
                m_AisSentence.m_OwnShipInfo.SetEstHour(24);
            }
            else
            {
                m_AisSentence.m_OwnShipInfo.SetEstHour(Convert.ToInt32(cb_VoyageHour.Text));
            }

            if (cb_VoyageMinute.SelectedIndex < 0)
            {
                m_AisSentence.m_OwnShipInfo.SetEstMinute(60);
            }
            else
            {
                m_AisSentence.m_OwnShipInfo.SetEstMinute(Convert.ToInt32(cb_VoyageMinute.Text));
            }

            if (cb_VoyageDay.SelectedIndex < 0)
            {
                m_AisSentence.m_OwnShipInfo.SetEstDay(0);   // Default
            }
            else
            {
                m_AisSentence.m_OwnShipInfo.SetEstDay(cb_VoyageDay.SelectedIndex + 1);
            }

            if (cb_VoyageMonth.SelectedIndex < 0)
            {
                m_AisSentence.m_OwnShipInfo.SetEstMonth(0);   // Default
            }
            else
            {
                m_AisSentence.m_OwnShipInfo.SetEstMonth(cb_VoyageMonth.SelectedIndex + 1);
            }

            if (cb_VoyageNavStatus.SelectedIndex < 0)
            {
                m_AisSentence.m_OwnShipInfo.SetNavStatus(12);   // Default
            }
            else 
            {
                m_AisSentence.m_OwnShipInfo.SetNavStatus(cb_VoyageNavStatus.SelectedIndex);
            }

            m_AisSentence.SendAISSentence(SENTENCE_TYPE.ST_VSD);
        }

        private void cb_StaticShipType_SelectedIndexChanged(object sender, EventArgs e)
        {
            switch (cb_StaticShipType.SelectedIndex)
            {
                case 0:
                    cb_VoyageCargo.Items.Clear();
                    cb_VoyageCargo.SelectedIndex = -1;
                    cb_VoyageCargo.Enabled = false;
                    break;
                case 3:
                    cb_VoyageCargo.Enabled = true;
                    cb_VoyageCargo.Items.Clear();
                    cb_VoyageCargo.Items.Add("0-Fishing");
                    cb_VoyageCargo.Items.Add("1-Towing");
                    cb_VoyageCargo.Items.Add("2-Towing and tow exceeds 200m");
                    cb_VoyageCargo.Items.Add("3-Engaged in dredging or underwater operations");
                    cb_VoyageCargo.Items.Add("4-Engaged in diving operations");
                    cb_VoyageCargo.Items.Add("5-Engaged in military operations");
                    cb_VoyageCargo.Items.Add("6-Sailing");
                    cb_VoyageCargo.Items.Add("7-Pleasure craft");
                    cb_VoyageCargo.Items.Add("8-Reserved for future use");
                    cb_VoyageCargo.Items.Add("9-Reserved for future use");
                    cb_VoyageCargo.SelectedIndex = 0;
                    break;
                case 5:
                    cb_VoyageCargo.Items.Clear();
                    cb_VoyageCargo.Enabled = false;
                    cb_StaticShipType.SelectedIndex = 0;
                    break;
                default:
                    cb_VoyageCargo.Enabled = true;
                    cb_VoyageCargo.Items.Clear();
                    cb_VoyageCargo.Items.Add("0-All ships of this type");
                    cb_VoyageCargo.Items.Add("1-Carrying DG, HS, or MP(X)");
                    cb_VoyageCargo.Items.Add("2-Carrying DG, HS, or MP(Y)");
                    cb_VoyageCargo.Items.Add("3-Carrying DG, HS, or MP(Z)");
                    cb_VoyageCargo.Items.Add("4-Carrying DG, HS, or MP(OS)");
                    cb_VoyageCargo.Items.Add("5-Reserved for future use");
                    cb_VoyageCargo.Items.Add("6-Reserved for future use");
                    cb_VoyageCargo.Items.Add("7-Reserved for future use");
                    cb_VoyageCargo.Items.Add("8-Reserved for future use");
                    cb_VoyageCargo.Items.Add("9-No additional information");
                    cb_VoyageCargo.SelectedIndex = 0;
                    break;
            }
        }

        private void btn_LogClear_Click(object sender, EventArgs e)
        {
            m_CounterTimer.Stop();
            m_CounterTimer.Start();

            m_CounterElapsedSeconds = 0;
            tb_logging.Clear();
            dgv_vdmcounter.Rows.Clear();
            m_AisSentence.ClearMsgCount();
            m_AisSentence2.ClearMsgCount();
        }

        private void btn_LogSave_Click(object sender, EventArgs e)
        {
            using (SaveFileDialog saveFileDialog = new SaveFileDialog())
            {
                saveFileDialog.Filter = "텍스트 파일 (*.txt)|*.txt|모든 파일 (*.*)|*.*";
                saveFileDialog.Title = "로그 저장";
                saveFileDialog.FileName = $"AIS_Log_{DateTime.Now:yyyyMMdd_HHmmss}.txt";

                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        System.IO.File.WriteAllText(saveFileDialog.FileName, tb_logging.Text, Encoding.UTF8);
                        MessageBox.Show("로그가 성공적으로 저장되었습니다.", "저장 완료", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show("로그 저장 중 오류가 발생했습니다.\n" + ex.Message, "오류", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void btn_LogClear2_Click(object sender, EventArgs e)
        {
            m_CounterTimer.Stop();
            m_CounterTimer.Start();

            m_CounterElapsedSeconds = 0;
            tb_logging2.Clear();
            dgv_vdmcounter2.Rows.Clear();
            m_AisSentence.ClearMsgCount();
            m_AisSentence2.ClearMsgCount();
        }

        private void btn_LogSave2_Click(object sender, EventArgs e)
        {
            using (SaveFileDialog saveFileDialog = new SaveFileDialog())
            {
                saveFileDialog.Filter = "텍스트 파일 (*.txt)|*.txt|모든 파일 (*.*)|*.*";
                saveFileDialog.Title = "로그 저장";
                saveFileDialog.FileName = $"AIS_Log_{DateTime.Now:yyyyMMdd_HHmmss}.txt";

                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        System.IO.File.WriteAllText(saveFileDialog.FileName, tb_logging2.Text, Encoding.UTF8);
                        MessageBox.Show("로그가 성공적으로 저장되었습니다.", "저장 완료", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show("로그 저장 중 오류가 발생했습니다.\n" + ex.Message, "오류", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void m_CounterTimer_Tick(object sender, EventArgs e)
        {
            int Ch1MsgCnt;
            int Ch2MsgCnt;

            m_CounterElapsedSeconds++;
            if (m_CounterElapsedSeconds % 60 == 0)
            {
                if (m_SerialPort != null && m_SerialPort.IsOpen)
                {
                    m_AisSentence.GetChannelCounter(out Ch1MsgCnt, out Ch2MsgCnt);
                    m_AisSentence.ClearMsgCount();
                    dgv_vdmcounter.Rows.Add(Ch1MsgCnt, Ch2MsgCnt, Ch1MsgCnt + Ch2MsgCnt, DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                }

                if (m_SerialPort2 != null && m_SerialPort2.IsOpen)
                {
                    m_AisSentence2.GetChannelCounter(out Ch1MsgCnt, out Ch2MsgCnt);
                    m_AisSentence2.ClearMsgCount();
                    dgv_vdmcounter2.Rows.Add(Ch1MsgCnt, Ch2MsgCnt, Ch1MsgCnt + Ch2MsgCnt, DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                }
            }
        }
    }
}
