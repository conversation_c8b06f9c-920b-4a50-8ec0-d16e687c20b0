﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using static AssistPro.vhf.DataProtocol;
using static AssistPro.vhf.DscMessage;

namespace AssistPro.vhf
{
    public partial class DscComposeForm : Form
    {

        public struct DscCompose
        {
            public bool m_IsAdd;
            public string msg_type;
            public string msg_format;
            public string msg_category;
            public string mmsi_call_to;
            public string mmsi_call_from;
            public string mmsi_distress;
            public string nature;
            public string teleCom1;
            public string teleCom2;
            public string teleCom;
            public string sub_com;
            public string freq;
            public string eos;
            public string latitude;
            public string longitude;
            public string utc;
            public string tel_num;
            public string expn_mode;
            public string area;
            public string area_corner;
            public string area_size;
        }

        public event EventHandler<DscCompose>DataReceived;

        public DscCompose m_DscMsg;

        public DscComposeForm(bool IsAdd)
        {
            InitializeComponent();
            cb_mmsi_callto.SelectedIndex = 0;
            cb_mmsi_call_from.SelectedIndex = 0;
            cb_mmsi_distress.SelectedIndex = 0;

            cb_msg_type.SelectedIndex = 0;
            cb_msg_nature.SelectedIndex = 0;
            cb_msg_sub_com.SelectedIndex = 0;

            cb_enhance.Checked = true;
            cb_lat.SelectedIndex = 0;
            cb_lon.SelectedIndex = 0;
            cb_expn_mode.SelectedIndex = 0;
            cb_area_lat.SelectedIndex = 0;
            cb_area_lon.SelectedIndex = 0;

            gb_area.Visible = false;
            
            m_DscMsg.m_IsAdd = IsAdd;
        }

        public void SetDefaultValue()
        {
            InitDistressAlert(); // init default
        }

        public void SetDscMsgValue(DscCompose dsc_msg)
        {
            if (dsc_msg.msg_type == null || dsc_msg.msg_type.Equals("DISTRESS"))
            {
                InitDistressAlert();
            }
            else if (dsc_msg.msg_type.Equals("DISTRESS ACK"))
            {
                InitDistressAlertAck();
            }
            else if (dsc_msg.msg_type.Equals("DISTRESS RELAY"))
            {
                InitDistressAlertRelay();
            }

            if (dsc_msg.msg_type != null)
            {
                UpdateMsgSetting(dsc_msg);
            }
        }

        private void UpdateMsgSetting(DscCompose dsc_msg)
        {
            cb_msg_type.Text = dsc_msg.msg_type.ToString();
            cb_msg_format.Text = dsc_msg.msg_format.ToString();
            cb_msg_category.Text = dsc_msg.msg_category.ToString();
            cb_msg_nature.Text = dsc_msg.nature.ToString();
            cb_msg_telecom1.Text = dsc_msg.teleCom1.ToString();
            cb_msg_telecom2.Text = dsc_msg.teleCom2.ToString();
            cb_msg_telecom.Text = dsc_msg.teleCom.ToString();
            cb_msg_sub_com.Text = dsc_msg.sub_com.ToString();
            cb_msg_eos.Text = dsc_msg.eos.ToString();
            masktb_lat.Text = dsc_msg.latitude.ToString();
            masktb_lon.Text = dsc_msg.longitude.ToString();
            masktb_utc.Text = dsc_msg.utc.ToString();

            if (dsc_msg.latitude.EndsWith("N"))
                cb_lat.SelectedIndex = 0;
            else
                cb_lat.SelectedIndex = 1;

            if (dsc_msg.longitude.EndsWith("E"))
                cb_lon.SelectedIndex = 0;
            else
                cb_lon.SelectedIndex = 1;


            if (dsc_msg.msg_format.Equals(FMT_102))
            {
                // "11°N111°E11°NS11°WE"

                string area_lat = dsc_msg.area.Substring(0, 2);
                masktb_area_lat.Text = area_lat;
                string area_ns = dsc_msg.area.Substring(3, 1);

                string area_lon = dsc_msg.area.Substring(4, 3);
                masktb_area_lon.Text = area_lon;
                string area_we = dsc_msg.area.Substring(8, 1);

                if (area_ns.Equals("N"))
                    cb_area_lat.SelectedIndex = 0;
                else
                    cb_area_lat.SelectedIndex = 1;

                if (area_we.Equals("E"))
                    cb_area_lon.SelectedIndex = 0;
                else
                    cb_area_lon.SelectedIndex = 1;

                string area_size_ns = dsc_msg.area.Substring(9, 2);
                masktb_area_size_ns.Text = area_size_ns;

                string area_size_we = dsc_msg.area.Substring(14, 2);
                masktb_area_size_we.Text = area_size_we;
            }
        }

        private void InitDistressAlert()
        {
            cb_msg_format.Visible = true;
            cb_msg_format.Enabled = false ;

            cb_msg_nature.Visible = true;
            cb_msg_nature.Enabled = true;

            cb_msg_sub_com.Visible = true;
            cb_msg_sub_com.Enabled = true;

            cb_msg_category.Visible = false;
            cb_msg_telecom1.Visible = false;
            cb_msg_telecom2.Visible = false;
            cb_msg_telecom.Visible = false;

            tb_freq.Visible = false;

            cb_msg_eos.Visible = true;
            cb_msg_eos.Enabled = false;

            tb_number.Visible = false;
            gb_area.Visible = false;

            cb_msg_type.SelectedIndex = 0;
            cb_msg_format.SelectedIndex = 1;

            cb_msg_nature.SelectedIndex = 0;
            cb_msg_sub_com.SelectedIndex = 0;
            cb_msg_eos.SelectedIndex = 2;
        }

        private void InitDistressAlertAck()
        {
            cb_msg_format.Visible = true;
            cb_msg_format.Enabled = false;

            cb_msg_category.Visible = true;
            cb_msg_category.Enabled = false;

            cb_msg_nature.Visible = true;
            cb_msg_nature.Enabled = true;

            cb_msg_sub_com.Visible = true;
            cb_msg_sub_com.Enabled = true;

            cb_msg_telecom.Visible = true;
            cb_msg_telecom.Enabled = false;

            cb_msg_telecom1.Visible = false;
            cb_msg_telecom2.Visible = false;

            tb_freq.Visible = false;

            cb_msg_eos.Visible = true;
            cb_msg_eos.Enabled = false;

            tb_number.Visible = false;
            gb_area.Visible = false;

            cb_msg_type.SelectedIndex = 1;
            cb_msg_format.SelectedIndex = 3;
            cb_msg_category.SelectedIndex = 4;
            cb_msg_telecom.SelectedIndex = 7;

            cb_msg_nature.SelectedIndex = 0;
            cb_msg_sub_com.SelectedIndex = 0;
            cb_msg_eos.SelectedIndex = 2;
        }

        private void InitDistressAlertRelay()
        {
            cb_msg_format.Visible = true;
            cb_msg_format.Enabled = true;

            cb_msg_category.Visible = true;
            cb_msg_category.Enabled = false;

            cb_msg_nature.Visible = true;
            cb_msg_nature.Enabled = true;

            cb_msg_sub_com.Visible = true;
            cb_msg_sub_com.Enabled = true;

            cb_msg_telecom.Visible = true;
            cb_msg_telecom.Enabled = false;

            cb_msg_telecom1.Visible = false;
            cb_msg_telecom2.Visible = false;

            tb_freq.Visible = false;
            gb_area.Visible = false;

            cb_msg_eos.Visible = true;
            cb_msg_eos.Enabled = true;

            tb_number.Visible = false;
            gb_area.Visible = true;

            cb_msg_type.SelectedIndex = 2;
            cb_msg_format.SelectedIndex = 4;
            cb_msg_category.SelectedIndex = 4;
            cb_msg_telecom.SelectedIndex = 8;

            cb_msg_nature.SelectedIndex = 0;
            cb_msg_sub_com.SelectedIndex = 0;
            cb_msg_eos.SelectedIndex = 2;
        }

        private void InitDistressAlertRelayAck()
        {
            cb_msg_format.Visible = true;
            cb_msg_format.Enabled = true;

            cb_msg_category.Visible = true;
            cb_msg_category.Enabled = false;

            cb_msg_nature.Visible = true;
            cb_msg_nature.Enabled = true;

            cb_msg_sub_com.Visible = true;
            cb_msg_sub_com.Enabled = true;

            cb_msg_telecom.Visible = true;
            cb_msg_telecom.Enabled = false;

            cb_msg_telecom1.Visible = false;
            cb_msg_telecom2.Visible = false;

            tb_freq.Visible = false;

            cb_msg_eos.Visible = true;
            cb_msg_eos.Enabled = false;

            tb_number.Visible = false;
            gb_area.Visible = false;

            cb_msg_type.SelectedIndex = 3;
            cb_msg_format.SelectedIndex = 4;
            cb_msg_category.SelectedIndex = 4;
            cb_msg_telecom.SelectedIndex = 8;

            cb_msg_nature.SelectedIndex = 0;
            cb_msg_sub_com.SelectedIndex = 0;
            cb_msg_eos.SelectedIndex = 1;
        }

        private void InitAllShips()
        {
            cb_msg_format.Visible = true;
            cb_msg_format.Enabled = false;

            cb_msg_category.Visible = true;
            cb_msg_category.Enabled = true;

            cb_msg_nature.Visible = false;
            cb_msg_sub_com.Visible = false;

            cb_msg_telecom.Visible = false;
            cb_msg_telecom1.Visible = true;
            cb_msg_telecom1.Enabled = true;

            cb_msg_telecom2.Visible = true;
            cb_msg_telecom2.Enabled = true;

            tb_freq.Visible = true;
            tb_freq.Enabled = true;

            cb_msg_eos.Visible = true;
            cb_msg_eos.Enabled = false;

            tb_number.Visible = false;
            gb_area.Visible = false;

            cb_msg_type.SelectedIndex = 4;
            cb_msg_format.SelectedIndex = 3;
            cb_msg_category.SelectedIndex = 2;
            cb_msg_telecom1.SelectedIndex = 0;
            cb_msg_telecom2.SelectedIndex = 12;

            cb_msg_eos.SelectedIndex = 2;
        }

        private void InitIndividual()
        {
            cb_msg_format.Visible = true;
            cb_msg_format.Enabled = false;

            cb_msg_category.Visible = true;
            cb_msg_category.Enabled = true;

            cb_msg_nature.Visible = false;
            cb_msg_sub_com.Visible = false;

            cb_msg_telecom.Visible = false;
            cb_msg_telecom1.Visible = true;
            cb_msg_telecom1.Enabled = true;

            cb_msg_telecom2.Visible = true;
            cb_msg_telecom2.Enabled = true;

            tb_freq.Visible = true;
            tb_freq.Enabled = true;

            cb_msg_eos.Visible = true;
            cb_msg_eos.Enabled = true;

            tb_number.Visible = false;
            gb_area.Visible = false;

            cb_msg_type.SelectedIndex = 5;
            cb_msg_format.SelectedIndex = 4;
            cb_msg_category.SelectedIndex = 2;
            cb_msg_telecom1.SelectedIndex = 0;
            cb_msg_telecom2.SelectedIndex = 12;

            cb_msg_eos.SelectedIndex = 0;
        }

        private void InitGroup()
        {
            cb_msg_format.Visible = true;
            cb_msg_format.Enabled = false;

            cb_msg_category.Visible = true;
            cb_msg_category.Enabled = false;

            cb_msg_nature.Visible = false;
            cb_msg_sub_com.Visible = false;

            cb_msg_telecom.Visible = false;
            cb_msg_telecom1.Visible = true;
            cb_msg_telecom1.Enabled = true;

            cb_msg_telecom2.Visible = true;
            cb_msg_telecom2.Enabled = false;

            tb_freq.Visible = true;
            tb_freq.Enabled = true;

            cb_msg_eos.Visible = true;
            cb_msg_eos.Enabled = false;

            tb_number.Visible = false;
            gb_area.Visible = false;

            cb_msg_type.SelectedIndex = 6;
            cb_msg_format.SelectedIndex = 2;
            cb_msg_category.SelectedIndex = 0;
            cb_msg_telecom1.SelectedIndex = 0;
            cb_msg_telecom2.SelectedIndex = 12;

            cb_msg_eos.SelectedIndex = 2;
        }

        private void InitSemiAuto()
        {
            cb_msg_format.Visible = true;
            cb_msg_format.Enabled = false;

            cb_msg_category.Visible = true;
            cb_msg_category.Enabled = false;

            cb_msg_nature.Visible = false;
            cb_msg_sub_com.Visible = false;

            cb_msg_telecom.Visible = false;
            cb_msg_telecom1.Visible = true;
            cb_msg_telecom1.Enabled = true;

            cb_msg_telecom2.Visible = true;
            cb_msg_telecom2.Enabled = true;

            tb_freq.Visible = true;
            tb_freq.Enabled = true;

            cb_msg_eos.Visible = true;
            cb_msg_eos.Enabled = true;

            tb_number.Visible = true;
            tb_number.Enabled = true;

            gb_area.Visible = false;

            cb_msg_type.SelectedIndex = 7;
            cb_msg_format.SelectedIndex = 6;
            cb_msg_category.SelectedIndex = 0;
            cb_msg_telecom1.SelectedIndex = 0;
            cb_msg_telecom2.SelectedIndex = 12;

            cb_msg_eos.SelectedIndex = 2;
        }

        private void Load_ComposeMsg(object sender, EventArgs e)
        {
        }

        private void btn_ok_Click(object sender, EventArgs e)
        {
            m_DscMsg.msg_type = cb_msg_type.Text;
            m_DscMsg.msg_format = cb_msg_format.Text;
            m_DscMsg.mmsi_call_to = cb_mmsi_callto.Text;
            m_DscMsg.mmsi_call_from = cb_mmsi_call_from.Text;
            m_DscMsg.mmsi_distress = cb_mmsi_distress.Text;
            m_DscMsg.msg_category = cb_msg_category.Text;
            m_DscMsg.nature = cb_msg_nature.Text;
            m_DscMsg.teleCom1 = cb_msg_telecom1.Text;
            m_DscMsg.teleCom2 = cb_msg_telecom2.Text;
            m_DscMsg.teleCom = cb_msg_telecom.Text;
            m_DscMsg.sub_com = cb_msg_sub_com.Text;

            m_DscMsg.freq = tb_freq.Text;
            m_DscMsg.eos = cb_msg_eos.Text;
            m_DscMsg.latitude = masktb_lat.Text;
            m_DscMsg.longitude = masktb_lon.Text;
            m_DscMsg.tel_num = tb_number.Text;
            m_DscMsg.expn_mode = cb_expn_mode.Text;

            m_DscMsg.area_corner = masktb_area_lat.Text;

            if (cb_area_lat.SelectedIndex == 0)
                m_DscMsg.area_corner += "N";
            else if (cb_area_lat.SelectedIndex == 1)
                m_DscMsg.area_corner += "S";

            m_DscMsg.area_corner += masktb_area_lon.Text;

            if (cb_area_lon.SelectedIndex == 0)
                m_DscMsg.area_corner += "E";
            else if (cb_area_lon.SelectedIndex == 1)
                m_DscMsg.area_corner += "W";

            m_DscMsg.area_size = masktb_area_size_ns.Text;
            m_DscMsg.area_size += "NS";
            m_DscMsg.area_size += masktb_area_size_we.Text;
            m_DscMsg.area_size += "WE";

            m_DscMsg.area = m_DscMsg.area_corner + m_DscMsg.area_size;

            if (cb_lat.SelectedIndex == 0)
                m_DscMsg.latitude += "N";
            else if (cb_lat.SelectedIndex == 1)
                m_DscMsg.latitude += "S";

            if (cb_lon.SelectedIndex == 0)
                m_DscMsg.longitude += "E";
            else if (cb_lon.SelectedIndex == 1)
                m_DscMsg.longitude += "W";

            m_DscMsg.utc = masktb_utc.Text;

            if (m_DscMsg.msg_type.Equals(TYPE_INDIVIDUAL))
            {
                if (m_DscMsg.teleCom1.Equals(TELCOM1_118)) // test
                {
                    m_DscMsg.freq = TELCOM2_126;
                }
                else if (m_DscMsg.teleCom1.Equals(TELCOM1_103)) // polling
                {
                    m_DscMsg.freq = TELCOM2_126;
                }
                else if (m_DscMsg.teleCom1.Equals(TELCOM1_121) && m_DscMsg.eos.Equals(EOS_117)) // POS REQ
                {
                    m_DscMsg.freq = "POS3";
                }
                else if (m_DscMsg.teleCom1.Equals(TELCOM1_121) && m_DscMsg.eos.Equals(EOS_122)) // POS ACK
                {
                    m_DscMsg.freq = "POS4";
                }
                else
                {
                    m_DscMsg.utc = "N/A";
                }
            }
            else
            {
                m_DscMsg.freq = tb_freq.Text;
            }

            DataReceived.Invoke(this, m_DscMsg);

            DataReceived = null;

            this.Close();
        }

        private void btn_cancel_Click(object sender, EventArgs e)
        {
            DataReceived = null;
            this.Close();
        }

        private void CheckedChanged_Enhance(object sender, EventArgs e)
        {
            if (cb_enhance.Checked)
            {
                masktb_lat.Mask = "00°00.0000'";
                masktb_lon.Mask = "000°00.0000'";
                masktb_lat.Text = "00000000";
                masktb_lon.Text = "000000000";
            }
            else
            {
                masktb_lat.Mask = "00°00'";
                masktb_lon.Mask = "000°00'";
                masktb_lat.Text = "0000";
                masktb_lon.Text = "00000";
            }
        }

        private void SelectedIndexChanged_MsgType(object sender, EventArgs e)
        {
            if (cb_msg_type.SelectedIndex == 0)
            {
                InitDistressAlert();
            }
            else if (cb_msg_type.SelectedIndex == 1)
            {
                InitDistressAlertAck();
            }
            else if (cb_msg_type.SelectedIndex == 2)
            {
                InitDistressAlertRelay();
            }
            else if (cb_msg_type.SelectedIndex == 3)
            {
                InitDistressAlertRelayAck();
            }
            else if (cb_msg_type.SelectedIndex == 4)
            {
                InitAllShips();
            }
            else if (cb_msg_type.SelectedIndex == 5)
            {
                InitIndividual();
            }
            else if (cb_msg_type.SelectedIndex == 6)
            {
                InitGroup();
            }
            else if (cb_msg_type.SelectedIndex == 7)
            {
                InitSemiAuto();
            }
        }

        private void SelectedIndexChanged_telecom1(object sender, EventArgs e)
        {
            if (cb_msg_telecom1.SelectedIndex == 2)
                tb_freq.Text = "126";
            else
                tb_freq.Text = "16";
        }

        static public void EncodeUTC(string InUtc, ref string OutUtc)
        {
            if (!InUtc.Equals("N/A"))
            {
                int utcIndex = InUtc.IndexOf(':');
                string Hour = InUtc.Substring(0, utcIndex);
                string Minutes = InUtc.Substring(utcIndex + 1, 2);

                OutUtc = Hour + Minutes;

                if (Hour == " " || Minutes == " ")
                    OutUtc = "8888";
            }
            else
               OutUtc = "8888";
        }

        static public string EncodeFreq(string data)
        {
            if (data.Length == 1)
                data = "0" + data;

            return "9000" + data + "9000" + data;
        }

        static public int EncodePhoneNumber(ref byte[] array, int iStart, string phone_no)
        {
            int ret = 0;
            // character (2 ~9) -> -1(number type(odd,even) -> (1~8)
            // so digit(2 ~16)
            if (phone_no.Length >= 2 && phone_no.Length <= 16)
            {
                if (phone_no.Length % 2 == 0)
                {
                    // even number
                    array[iStart] = 106;
                    for (int i = 0; i < (phone_no.Length / 2); i++)
                    {
                        array[iStart + i + 1] = (byte)((phone_no[2 * i] - '0') * 10 + (phone_no[2 * i + 1] - '0'));
                    }
                    ret = phone_no.Length / 2 + 1;
                }
                else
                {
                    // odd number
                    array[iStart] = 105;
                    for (int i = 0; i < (phone_no.Length / 2) + 1; i++)
                    {
                        if (i == 0)
                        {
                            array[iStart + i + 1] = (byte)((phone_no[2 * i] - '0'));
                        }
                        else
                        {
                            array[iStart + i + 1] = (byte)((phone_no[2 * i - 1] - '0') * 10 + (phone_no[2 * i] - '0'));
                        }
                    }
                    ret = phone_no.Length / 2 + 2;
                }
            }

            return ret;
        }

        static public void EncodePos1(string latitude, string longitude, ref string Position, ref string LatExpnPos, ref string LonExpnPos)
        {
            // Distress Coordinates
            // "11°11.1111'N"
            int degreeIndex = latitude.IndexOf('°');
            int dotIndex = latitude.IndexOf('.');
            int minuteindex = latitude.IndexOf('\'');
            int directionIndex = latitude.Length - 1;

            string latitudeDegrees;
            string latitudeMinutes;
            //string latitudeMinutesExpn;
            char latitudeDirection = 'N';

            if (dotIndex < 0) // not expand format
            {
                latitudeDegrees = latitude.Substring(0, degreeIndex);
                latitudeMinutes = latitude.Substring(degreeIndex + 1, 2);
                LatExpnPos = null;
                latitudeDirection = latitude[directionIndex];
            }
            else
            {
                latitudeDegrees = latitude.Substring(0, degreeIndex);
                latitudeMinutes = latitude.Substring(degreeIndex + 1, dotIndex - degreeIndex - 1);
                LatExpnPos = latitude.Substring(dotIndex + 1, minuteindex - dotIndex - 1);
                latitudeDirection = latitude[directionIndex];
            }

            degreeIndex = longitude.IndexOf('°');
            dotIndex = longitude.IndexOf('.');
            minuteindex = longitude.IndexOf('\'');
            directionIndex = longitude.Length - 1;

            string longitudeDegrees;
            string longitudeMinutes;
            //string longitudeMinutesExpn;
            char longitudeDirection = 'E';

            if (dotIndex < 0) // not expand format
            {
                longitudeDegrees = longitude.Substring(0, degreeIndex);

                longitudeMinutes = longitude.Substring(degreeIndex + 1, 2);
                LonExpnPos = null;
                latitudeDirection = longitude[directionIndex];
            }
            else
            {
                longitudeDegrees = longitude.Substring(0, degreeIndex);
                longitudeMinutes = longitude.Substring(degreeIndex + 1, dotIndex - degreeIndex - 1);
                LonExpnPos = longitude.Substring(dotIndex + 1, minuteindex - dotIndex - 1);

                longitudeDirection = longitude[directionIndex];
            }

            string dir;
            Position = null;

            if (latitudeDirection == 'N' && longitudeDirection == 'E')
                dir = "0";
            else if (latitudeDirection == 'N' && longitudeDirection == 'W')
                dir = "1";
            else if (latitudeDirection == 'S' && longitudeDirection == 'E')
                dir = "2";
            else if (latitudeDirection == 'S' && longitudeDirection == 'W')
                dir = "3";
            else
                dir = "0";

            Position += dir;
            Position += latitudeDegrees + latitudeMinutes;

            Position += longitudeDegrees + longitudeMinutes;


            if (latitudeDegrees == " " || latitudeMinutes == " ")
                Position = "9999999999";

            if (longitudeDegrees == " " || longitudeDegrees == " ")
                Position = "9999999999";
        }

        static public void EncodePos4(string latitude, string longitude, ref string Position, ref string LatExpnPos, ref string LonExpnPos)
        {
            // Distress Coordinates
            // "11°11.1111'N"
            int degreeIndex = latitude.IndexOf('°');
            int dotIndex = latitude.IndexOf('.');
            int minuteindex = latitude.IndexOf('\'');
            int directionIndex = latitude.Length - 1;

            string latitudeDegrees;
            string latitudeMinutes;
            //string latitudeMinutesExpn;
            char latitudeDirection = 'N';

            if (dotIndex < 0) // not expand format
            {
                latitudeDegrees = latitude.Substring(0, degreeIndex);
                latitudeMinutes = latitude.Substring(degreeIndex + 1, 2);
                LatExpnPos = null;
                latitudeDirection = latitude[directionIndex];
            }
            else
            {
                latitudeDegrees = latitude.Substring(0, degreeIndex);
                latitudeMinutes = latitude.Substring(degreeIndex + 1, dotIndex - degreeIndex - 1);
                LatExpnPos = latitude.Substring(dotIndex + 1, minuteindex - dotIndex - 1);
                latitudeDirection = latitude[directionIndex];
            }

            degreeIndex = longitude.IndexOf('°');
            dotIndex = longitude.IndexOf('.');
            minuteindex = longitude.IndexOf('\'');
            directionIndex = longitude.Length - 1;

            string longitudeDegrees;
            string longitudeMinutes;
            //string longitudeMinutesExpn;
            char longitudeDirection = 'E';

            if (dotIndex < 0) // not expand format
            {
                longitudeDegrees = longitude.Substring(0, degreeIndex);

                longitudeMinutes = longitude.Substring(degreeIndex + 1, 2);
                LonExpnPos = null;
                latitudeDirection = longitude[directionIndex];
            }
            else
            {
                longitudeDegrees = longitude.Substring(0, degreeIndex);
                longitudeMinutes = longitude.Substring(degreeIndex + 1, dotIndex - degreeIndex - 1);
                LonExpnPos = longitude.Substring(dotIndex + 1, minuteindex - dotIndex - 1);

                longitudeDirection = longitude[directionIndex];
            }

            string dir;
            Position = null;

            if (latitudeDirection == 'N' && longitudeDirection == 'E')
                dir = "0";
            else if (latitudeDirection == 'N' && longitudeDirection == 'W')
                dir = "1";
            else if (latitudeDirection == 'S' && longitudeDirection == 'E')
                dir = "2";
            else if (latitudeDirection == 'S' && longitudeDirection == 'W')
                dir = "3";
            else
                dir = "0";

            Position += dir;
            Position += latitudeDegrees + latitudeMinutes;

            Position += longitudeDegrees + longitudeMinutes;


            if (latitudeDegrees == " " || latitudeMinutes == " ")
                Position = "9999999999";

            if (longitudeDegrees == " " || longitudeDegrees == " ")
                Position = "9999999999";

        }

        static public string EncodeArea(ref string area)
        {

            // "11°N111°E11°NS11°WE"
            string compose_area = null;

            string area_lat = area.Substring(0, 2);
            string area_ns = area.Substring(3, 1);

            string area_lon = area.Substring(4, 3);
            string area_we = area.Substring(8, 1);

            string area_size_ns = area.Substring(9, 2);

            string area_size_we = area.Substring(14, 2);

            if (area_ns.Equals("N") && area_we.Equals("W"))
                compose_area = "1";
            else if (area_ns.Equals("S") && area_we.Equals("E"))
                compose_area = "2";
            else if (area_ns.Equals("S") && area_we.Equals("W"))
                compose_area = "3";
            else
                compose_area = "0";

            compose_area += area_lat + area_lon + area_size_ns + area_size_we;
 
            return compose_area;
        }
    }
}
