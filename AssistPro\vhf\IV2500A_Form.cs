﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.IO.Ports;
using System.Linq;
using System.Net;
using System.Net.NetworkInformation;
using System.Reflection.Emit;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using WeifenLuo.WinFormsUI;
using static AssistPro.ProtocolAnalyzerForm;
using static AssistPro.SerialManager;
using static AssistPro.vhf.DataProtocol;
using static AssistPro.vhf.DscComposeForm;
using static AssistPro.vhf.DscMessage;

using static System.Windows.Forms.AxHost;
using static System.Windows.Forms.VisualStyles.VisualStyleElement;

namespace AssistPro.vhf
{
    public partial class IV2500A_Form : DockContent
    {

        public struct system_info
        {
            public bool system_init;
            public bool trans_connected;
            public string trans_ver;
        }

        public struct dsc_send_info
        {
            public int sent_count;
            public int num_of_runs;
            public bool auto_send_toggle;

        }


        private SerialManager m_SerialManager;
        private SerialPort m_SerialPort;

        private byte m_Sender;
        private byte m_Receiver;
        private DataProtocol m_DataProtocol;
        private DscMessage m_DscMessage;

        private uint m_TotalCnt;
        private uint m_RetryCnt;
        private uint m_TimeOutCnt;
        private double m_HitRate;
        private string m_elapsedTime;
        private Stopwatch m_stopwatch;
        private Random m_rand;
        private Thread m_serialThread;
        private double m_startTimerMili;
        private Thread m_mainTaskThread;
        static public system_info m_sys_info;
        static public dsc_send_info m_dsc_send_info;
        private byte m_limiter_value;
        private byte m_gain_value;
        private byte m_att_value;

        private byte m_front_mk_value;
        private byte m_rear_mk_value;
        private byte m_wing_mk_value;
        private byte m_rx_bb_value;

        private byte m_front_h_spk_value;
        private byte m_rear_h_spk_value;
        private byte m_rear_wing_h_spk_value;
        private byte m_rx_bb_out_value;

        private int m_dsc_edit_row_idx;
        DscCompose m_SendDscMsg;
        DscComposeForm m_DscForm;

        private byte m_track_number;
        private bool m_selfTestToggle;

        private int m_playbackTime;
        private int m_new_playbackTime;
        private uint[] m_AudioTrackRecordMS;

        private Thread m_selfTestThread;
        private int m_selftest_tot_pass_count = 0;
        private int m_selftest_tot_fail_count = 0;
    

        public delegate void UpdateConnectStatus();
        public delegate void UpdateDscStatus();

        public delegate void UpdateFullExtCommStatus();
        public delegate void UpdatePosTmStatus();

        public delegate void UpdateAudioTrackInfoStatus();
        public delegate void UpdateRecordDoneStatus();

        public UpdateConnectStatus ConnectStatus;
        public UpdateDscStatus DscStatus;
        public UpdateFullExtCommStatus FullExtCommStatus;

        public UpdatePosTmStatus PosTmStatus;
        public UpdateAudioTrackInfoStatus AudioTrackInfoStatus;
        public UpdateRecordDoneStatus RecordDoneStatus;

        public delegate void UpdateSelfTestStatus();
        public UpdateSelfTestStatus SelfTestStatus;

        public IV2500A_Form()
        {
            InitializeComponent();
            ConnectStatus += BB_ConnectStatus;

        }

        private void TabPageOnOff(bool isOn)
        {
            tabPage1.Enabled = isOn;
            tabPage2.Enabled = isOn;
            tabPage3.Enabled = isOn;
            tabPage4.Enabled = isOn;
            tabPage5.Enabled = isOn;
            tabPage6.Enabled = isOn;
        }

        private void Load_VhfSimulator(object sender, EventArgs e)
        {
            foreach (string name in SerialPort.GetPortNames())
            {
                cbCom.Items.Add(name);
            }
            m_Sender = DataProtocol.CONTROLLER;
            m_Receiver = DataProtocol.TRANSCEIVER;

            m_stopwatch = new Stopwatch();
            m_rand = new Random();

            m_DscMessage = new DscMessage();
            DscStatus += DscMsgStatus;
            FullExtCommStatus += FullExtCommSentence;
            PosTmStatus += UpdatePosTm;
            AudioTrackInfoStatus += UpdateAudioTrackInfo;
            RecordDoneStatus += UpdateRecordDone;
            SelfTestStatus += UpdateSelfTestResult;

            this.dgvDscRsv.Font = new Font("휴먼모음T", 9, FontStyle.Regular);
            this.dgvDscSend.Font = new Font("휴먼모음T", 9, FontStyle.Regular);

            //register delegate
            DataProtocol.evtNotiDscMsgReceived += NotiDsdMsgReceived;
            DataProtocol.evtNotiFullExtCommReceived += NotiFullExtCommReceived;
            DataProtocol.evtNotiPosTmReceived += NotiPosTmReceived;
            DataProtocol.evtNotiAudioTrackInfoReceived += NotiAudioTrackInfoReceived;
            DataProtocol.evtNotiRecordDoneReceived += NotiRecordDoneReceived;
            DataProtocol.evtNotiSelfTestReceived += NotiSelfTestReceived;

            TabPageOnOff(false);

            btn_start.Enabled = true;
            btn_stop.Enabled = false;

            m_limiter_value = 0;
            m_gain_value = 0;
            m_att_value = 0;

            m_front_mk_value = 2;
            m_rear_mk_value = 2;
            m_wing_mk_value = 2;
            m_rx_bb_value = 2;

            m_front_h_spk_value = 0;
            m_rear_h_spk_value = 0;
            m_rear_wing_h_spk_value = 0;
            m_rx_bb_out_value = 0;

            btnDscEdit.Visible = false;
            bt_notify.Enabled = false;
            rb_raise.Checked = true;

            btnSetController.Enabled = false;

            SetRxTxMainDDS_UI();

            m_AudioTrackRecordMS = new uint[TRACK_INFO_SIZE];

            m_DscForm = new DscComposeForm(true);

        }

        private void NotiDsdMsgReceived()
        {
            Invoke(DscStatus);
        }

        private void NotiFullExtCommReceived()
        {
            Invoke(FullExtCommStatus);
        }

        private void NotiPosTmReceived()
        {
            Invoke(PosTmStatus);
        }

        private void NotiAudioTrackInfoReceived()
        {
            Invoke(AudioTrackInfoStatus);
        }

        private void NotiRecordDoneReceived()
        {
            Invoke(RecordDoneStatus);
        }

        private void NotiSelfTestReceived()
        {
            Invoke(SelfTestStatus);
        }

        private void serialPort_DataReceived(object sender, SerialDataReceivedEventArgs e)
        {
            this.Invoke(new EventHandler(SerialData_Received));
        }

        private void SerialData_Received(object s, EventArgs e)
        {
            m_SerialManager.Enqueue();
        }

        private void btnConnect_Click(object sender, EventArgs e)
        {
            try
            {
                if (cbCom.Text == "")
                {
                    MessageBox.Show(" You need select port. ");
                    return;
                }

                if (cbCom.Enabled)
                {
                    m_SerialManager = new SerialManager(cbCom.Text, 115200);
                    m_SerialPort = m_SerialManager.GetInstance();
                    m_SerialPort.DataReceived += serialPort_DataReceived;
                    m_SerialPort.Open();

                    btnConnect.Text = "DISCONNECT";
                    cbCom.Enabled = false;
                    btnConnect.BackColor = Color.PaleGreen;

                    m_DataProtocol = new DataProtocol(m_SerialPort);

                    m_serialThread = new Thread(SerialDataThread);
                    m_serialThread.Priority = ThreadPriority.Highest;
                    m_serialThread.Start();

                    m_mainTaskThread = new Thread(MainTaskTread);
                    m_mainTaskThread.Start();

                    timer_connect.Start();
                }
                else
                {
                    m_SerialPort.Close();
                    btnConnect.Text = "CONNECT";
                    cbCom.Enabled = true;
                    btnConnect.BackColor = Color.Silver;

                    TabPageOnOff(false);

                    m_serialThread.Abort();
                    m_mainTaskThread.Abort();
                    timer_connect.Stop();
                    m_sys_info.trans_connected = false;
                }
            }
            catch (Exception ea)
            {
                MessageBox.Show(ea.Message);
                cbCom.Enabled = true;
                btnConnect.BackColor = Color.Silver;
                btnConnect.Text = "CONNECT";
            }
        }

        private void SettingOpMode()
        {
            if (rb_RxMode.Checked == true)
            {
                SendBBSetFrameData((int)BB_SET.OPMODE, (byte)0);
                Thread.Sleep(100);
                float.TryParse(tb_main_rx_freq.Text, out float freq);
                m_DataProtocol.m_SetRfData.MainDdsFreq = (uint)Math.Round(freq * 1000);
                m_DataProtocol.m_SetRfData.MainRxPower = 31;
                m_DataProtocol.m_SetRfData.MainTxPower = 0;
                SendRF_DdsFreqData();
            }
            else if (rb_TxMode.Checked == true)
            {
                SendBBSetFrameData((int)BB_SET.OPMODE, (byte)1);
                Thread.Sleep(100);
                float.TryParse(tb_main_tx_freq.Text, out float freq);
                m_DataProtocol.m_SetRfData.MainDdsFreq = (uint)Math.Round(freq * 1000);
                m_DataProtocol.m_SetRfData.MainRxPower = 0;
                m_DataProtocol.m_SetRfData.MainTxPower = 31;
                SendRF_DdsFreqData();
            }
            else if (rb_DotMode.Checked == true)
            {
                SendBBSetFrameData((int)BB_SET.OPMODE, (byte)3);
                Thread.Sleep(100);
                float.TryParse(tb_main_tx_freq.Text, out float freq);
                m_DataProtocol.m_SetRfData.MainDdsFreq = (uint)Math.Round(freq * 1000);
                m_DataProtocol.m_SetRfData.MainRxPower = 0;
                m_DataProtocol.m_SetRfData.MainTxPower = 31;
                SendRF_DdsFreqData();
            }
            else if (rb_MarkMode.Checked == true)
            {
                SendBBSetFrameData((int)BB_SET.OPMODE, (byte)4);
                Thread.Sleep(100);
                float.TryParse(tb_main_tx_freq.Text, out float freq);
                m_DataProtocol.m_SetRfData.MainDdsFreq = (uint)Math.Round(freq * 1000);
                m_DataProtocol.m_SetRfData.MainRxPower = 0;
                m_DataProtocol.m_SetRfData.MainTxPower = 31;
                SendRF_DdsFreqData();
            }
            else if (rb_SpaceMode.Checked == true)
            {
                SendBBSetFrameData((int)BB_SET.OPMODE, (byte)5);
                Thread.Sleep(100);
                float.TryParse(tb_main_tx_freq.Text, out float freq);
                m_DataProtocol.m_SetRfData.MainDdsFreq = (uint)Math.Round(freq * 1000);
                m_DataProtocol.m_SetRfData.MainRxPower = 0;
                m_DataProtocol.m_SetRfData.MainTxPower = 31;
                SendRF_DdsFreqData();
            }
        }

        private void SetRxTxMainDDS_UI()
        {
            if (rb_RxMode.Checked == true)
            {
                tb_main_rx_pwr.Value = 31;
                tb_main_tx_pwr.Value = 0;
                tb_main_rx_freq.Enabled = true;
                tb_main_rx_pwr.Enabled = true;
                tb_main_tx_freq.Enabled = false;
                tb_main_tx_pwr.Enabled = false;

            }
            else if (rb_TxMode.Checked == true || rb_DotMode.Checked == true || rb_MarkMode.Checked == true || rb_SpaceMode.Checked == true)
            {
                tb_main_tx_pwr.Value = 31;
                tb_main_rx_pwr.Value = 0;
                tb_main_tx_freq.Enabled = true;
                tb_main_tx_pwr.Enabled = true;
                tb_main_rx_freq.Enabled = false;
                tb_main_rx_pwr.Enabled = false;
            }
            lb_main_rx_pwr.Text = tb_main_rx_pwr.Value.ToString();
            lb_main_tx_pwr.Text = tb_main_tx_pwr.Value.ToString();
        }

        private void rb_OPmode_CheckedChanged(object sender, EventArgs e)
        {
            SetRxTxMainDDS_UI();
            SettingOpMode();
        }

        private void btn_stop_Click(object sender, EventArgs e)
        {
            timer_connect.Start();

            m_stopwatch.Stop();
            tick_timer.Stop();
            m_stopwatch.Reset();
            //m_serialThread.Abort();
            btn_start.Enabled = true;
            btn_stop.Enabled = false;
        }

        private void btn_start_Click(object sender, EventArgs e)
        {
            decimal interval = numericUpDown1.Value;

            timer_connect.Stop();

            tick_timer.Interval = (int)interval;
            tick_timer.Start();
            m_stopwatch.Start();

            m_startTimerMili = m_stopwatch.Elapsed.TotalMilliseconds;
            btn_start.Enabled = false;
            btn_stop.Enabled = true;

        }

        private void SerialDataThread()
        {
            byte TempData = 0;

            while (true)
            {
                while (m_SerialManager.Dequeue(ref TempData) != BUFF_STATUS.BUFF_NULL_DATA)
                {
                    if (m_DataProtocol.m_RcvFrame.bReceiving == false)
                    {
                        if (TempData == DataProtocol.FRM_SFD)
                        {
                            m_DataProtocol.ResetReceiveFrame();
                            m_DataProtocol.m_RcvFrame.rx_data[m_DataProtocol.m_RcvFrame.rcv_cnt++] = TempData;
                            m_DataProtocol.m_RcvFrame.bReceiving = true;
                        }
                    }
                    else
                    {
                        m_DataProtocol.m_RcvFrame.rx_data[m_DataProtocol.m_RcvFrame.rcv_cnt++] = TempData;
                        if (m_DataProtocol.m_RcvFrame.rcv_cnt == 3)
                        {
                            m_DataProtocol.m_RcvFrame.frame_len = (ushort)((m_DataProtocol.m_RcvFrame.rx_data[1])
                                                                          | ((m_DataProtocol.m_RcvFrame.rx_data[2] << 8) & 0xFF00));

                            if (m_DataProtocol.m_RcvFrame.frame_len < DataProtocol.FRAME_HEADER_SIZE ||
                                m_DataProtocol.m_RcvFrame.frame_len > DataProtocol.MAX_FRAME_LEN)
                            {
                                m_DataProtocol.ResetReceiveFrame();
                            }
                        }
                        else if (m_DataProtocol.m_RcvFrame.rcv_cnt >= m_DataProtocol.m_RcvFrame.frame_len + DataProtocol.FRAME_CRC_SIZE + 1)  // CRC(2) + FRM_SFD(1)
                        {
                            switch (m_DataProtocol.ParseCommand(m_DataProtocol.m_RcvFrame.rx_data, m_DataProtocol.m_RcvFrame.frame_len, m_DataProtocol.m_RcvFrame.rcv_cnt))
                            {
                                case DataProtocol.ERROR.ERR_CRC:
                                    {
                                        Console.WriteLine("ERROR.ERR_CRC");
                                        m_DataProtocol.ResetReceiveFrame();
                                        m_DataProtocol.m_DataStatCnt.fail_cnt++;
                                        break;
                                    }
                                case DataProtocol.ERROR.ERR_LEN:
                                    {
                                        Console.WriteLine("ERROR.ERR_LEN");
                                        m_DataProtocol.ResetReceiveFrame();
                                        m_DataProtocol.m_DataStatCnt.fail_cnt++;
                                    }
                                    break;
                                case DataProtocol.ERROR.ERR_SFD:
                                    {
                                        Console.WriteLine("ERROR.ERR_SFD");
                                        m_DataProtocol.ResetReceiveFrame();
                                        m_DataProtocol.m_DataStatCnt.fail_cnt++;
                                    }
                                    break;
                                case DataProtocol.ERROR.ERR_OK:
                                    m_DataProtocol.m_DataStatCnt.pass_cnt++;
                                    if (m_DataProtocol.m_DataFrame.RxFrameIdx > 0)
                                    {
                                        m_DataProtocol.m_DataFrame.RxFrameIdx = 0;
                                        m_DataProtocol.ProcCommandHandler();
                                    }
                                    break;
                                default:
                                    break;
                            }
                            m_DataProtocol.ResetReceiveFrame();
                        }
                    }
                }
                //Console.WriteLine("Thread");
                Thread.Sleep(1);
            }
        }


        private void numericUpDown1_ValueChanged(object sender, EventArgs e)
        {
            decimal interval = numericUpDown1.Value;

            if (interval > 0)
                tick_timer.Interval = (int)interval;

        }

        private void UpdateDiagComInfo()
        {
            lb_count.Text = m_TotalCnt.ToString();
            lb_rate.Text = m_HitRate.ToString("F2") + "%";
            lb_time.Text = m_elapsedTime;
            lb_timeout.Text = m_TimeOutCnt.ToString();
            lb_retry.Text = m_RetryCnt.ToString();
            lb_pass.Text = m_DataProtocol.m_DataStatCnt.pass_cnt.ToString();
            lb_data_bad.Text = m_DataProtocol.m_DataStatCnt.fail_cnt.ToString();
        }

        private void bt_clear_Click(object sender, EventArgs e)
        {
            m_SerialManager.init();
            m_DataProtocol.Init();
            m_DataProtocol.ClearStatCnt();
            m_TimeOutCnt = 0;
            m_RetryCnt = 0;
            m_TotalCnt = 0;
            m_elapsedTime = "0:0:0.0";
            m_HitRate = 0.0f;
            numericUpDown1.Value = 50;
            UpdateDiagComInfo();
        }

        private void gain_trackbar_scroll(object sender, EventArgs e)
        {
            // 0 ~ 30
            tb_gain.Text = gain_trackBar.Value.ToString();

            //float gain = 1.0f + ((float)gain_trackBar.Value * 0.033f);
            float gain = (float)gain_trackBar.Value * 0.33f;

            lb_gain_value.Text = "OUTPUT *" + gain.ToString("F3");

            m_gain_value = (byte)gain_trackBar.Value;

            SendBBSetFrameData((int)BB_SET.GAIN, m_gain_value);
        }

        private void att_trackbar_scroll(object sender, EventArgs e)
        {
            tb_att.Text = att_trackBar.Value.ToString();

            float att = 1.0f - ((float)att_trackBar.Value * 0.033f);

            lb_att_value.Text = "OUTPUT *" + att.ToString("F3");

            m_att_value = (byte)att_trackBar.Value;

            SendBBSetFrameData((int)BB_SET.ATT, m_att_value);
        }

        private void tb_gain_keydown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter) // 엔터 키를 확인
            {
                try
                {
                    int value = int.Parse(tb_gain.Text.ToString());
                    if (value < 0 || value > 500)
                    {
                        MessageBox.Show("GAIN : 0 <= value <= 500 is range, enterning value is wrong");
                        return;
                    }

                    gain_trackBar.Value = int.Parse(tb_gain.Text.ToString());

//                    float gain = 1.0f + ((float)gain_trackBar.Value * 0.033f);
                    float gain = (float)gain_trackBar.Value * 0.033f;

                    lb_gain_value.Text = "OUTPUT *" + gain.ToString("F3");

                    SendBBSetFrameData((int)BB_SET.GAIN, (byte)gain_trackBar.Value);

                    e.Handled = true; // 이벤트 처리가 완료됨을 표시
                    e.SuppressKeyPress = true; // 엔터 키가 TextBox에 추가 입력되지 않도록 방지
                }
                catch (FormatException)
                {
                    MessageBox.Show("Please enter a valid number.", "Input Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    tb_gain.Clear();
                    tb_gain.Focus();
                }
            }
        }

        private void tb_att_keydown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter) // 엔터 키를 확인
            {
                try
                {
                    int value = int.Parse(tb_att.Text.ToString());
                    if (value < 0 || value > 200)
                    {
                        MessageBox.Show("ATT : 0 <= value <= 100 is range, enterning value is wrong");
                        return;
                    }

                    att_trackBar.Value = int.Parse(tb_att.Text.ToString());

                    float att = 1.0f - ((float)att_trackBar.Value * 0.033f);

                    lb_att_value.Text = "OUTPUT *" + att.ToString("F3");

                    SendBBSetFrameData((int)BB_SET.ATT, (byte)att_trackBar.Value);

                    e.Handled = true; // 이벤트 처리가 완료됨을 표시
                    e.SuppressKeyPress = true; // 엔터 키가 TextBox에 추가 입력되지 않도록 방지
                }
                catch (FormatException)
                {
                    MessageBox.Show("Please enter a valid number.", "Input Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    tb_att.Clear();
                    tb_att.Focus();
                }

            }
        }

        private void SettingLimiter()
        {
            int value = int.Parse(tb_limiter.Text.ToString());
            if (value > 0 || value < -50)
            {
                MessageBox.Show("Limiter : -50.0 <= value <= 0.0 is range, enterning value is wrong");
                tb_limiter.Clear();
                tb_limiter.Focus();
                return;
            }

            //if (decimal.TryParse(tb_limiter.Text, out decimal result))
            //{
                // 소수점 이하 자릿수를 검사
            //    string[] parts = tb_limiter.Text.Split('.');
            //    if (parts.Length > 1 && parts[1].Length > 1)
            //    {
                    // 소수점 이하 자릿수가 1자리를 초과하면 이전 상태로 복원
            //        tb_limiter.Text = parts[0] + "." + parts[1].Substring(0, 1);
                    // 커서를 텍스트 끝으로 이동
            //        tb_limiter.SelectionStart = tb_limiter.Text.Length;
            //    }
            //}

            m_limiter_value = (byte)(value + 50);
            SendBBSetFrameData((int)BB_SET.LIMITER, m_limiter_value);
        }

        private void tb_limiter_keydown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter) // 엔터 키를 확인
            {
                try
                {
                    SettingLimiter();

                    e.Handled = true; // 이벤트 처리가 완료됨을 표시
                    e.SuppressKeyPress = true; // 엔터 키가 TextBox에 추가 입력되지 않도록 방지
                }
                catch (FormatException)
                {
                    MessageBox.Show("Please enter a valid number.", "Input Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    tb_limiter.Clear();
                    tb_limiter.Focus();
                }
            }
        }

        private void SendCodecSetFrameData(int data_idx, byte value)
        {
            ushort cmd;
            byte cmd_type = DataProtocol.CMD_SET_NOTI;
            ushort cmd_param = DataProtocol.CMD_CODEC_SET;
            cmd = m_DataProtocol.GenerateCMD(cmd_type, m_Sender, m_Receiver);

            m_DataProtocol.m_CodecSetData[data_idx] = value;

            if (m_DataProtocol.CheckStatus() == PROTO_STAT.IDLE_STAT)
            {
                m_DataProtocol.SendDataFrame(cmd, cmd_param, m_DataProtocol.m_CodecSetData, (int)CODEC_SET.MAX_CODEC_SET);
                m_DataProtocol.SetProtocoStatus(PROTO_STAT.WAIT_FOR_ACK);
            }
        }

        private void SendSelftestFrameData(int data_idx, byte value, byte run_mode)
        {
            ushort cmd;
            byte cmd_type = DataProtocol.CMD_SET_NOTI;
            ushort cmd_param = DataProtocol.CMD_SET_SELF_TEST;
            cmd = m_DataProtocol.GenerateCMD(cmd_type, m_Sender, m_Receiver);

            m_DataProtocol.m_DscLoopTestData[data_idx] = value;
            m_DataProtocol.m_DscLoopTestData[data_idx + 1] = run_mode;

            if (m_DataProtocol.CheckStatus() == PROTO_STAT.IDLE_STAT)
            {
                m_DataProtocol.SendDataFrame(cmd, cmd_param, m_DataProtocol.m_DscLoopTestData, (int)DSC_LOOP_TEST.MAX_DSC_LOOP_TEST);
                m_DataProtocol.SetProtocoStatus(PROTO_STAT.WAIT_FOR_ACK);
            }
        }

        private void SendVolSetFrameData(byte select_ch, int data_idx, byte value)
        {
            ushort cmd;
            byte cmd_type = DataProtocol.CMD_SET_NOTI;
            ushort cmd_param = DataProtocol.CMD_VOL_SET;
            cmd = m_DataProtocol.GenerateCMD(cmd_type, m_Sender, m_Receiver);

            m_DataProtocol.m_VolSetData[(int)VOL_SET.SELECT_CH] = (byte)select_ch;
            m_DataProtocol.m_VolSetData[data_idx] = value;

            if (m_DataProtocol.CheckStatus() == PROTO_STAT.IDLE_STAT)
            {
                m_DataProtocol.SendDataFrame(cmd, cmd_param, m_DataProtocol.m_VolSetData, (int)VOL_SET.MAX_VOL_SET);
                m_DataProtocol.SetProtocoStatus(PROTO_STAT.WAIT_FOR_ACK);
            }
        }


        private void SendAudioTrackSetFrameData(byte track_number, byte status, int playback_pos, byte record_on)
        {
            ushort cmd;
            byte cmd_type = DataProtocol.CMD_SET_NOTI;
            ushort cmd_param = DataProtocol.CMD_AUDIO_TRACK_SET;
            cmd = m_DataProtocol.GenerateCMD(cmd_type, m_Sender, m_Receiver);

            m_DataProtocol.m_DataFrame.data_buf[0] = status;
            m_DataProtocol.m_DataFrame.data_buf[1] = track_number;
            m_DataProtocol.m_DataFrame.data_buf[2] = record_on;
            m_DataProtocol.m_DataFrame.data_buf[3] = 0;
            m_DataProtocol.m_DataFrame.data_buf[4] = (byte)(playback_pos & 0xFF);
            m_DataProtocol.m_DataFrame.data_buf[5] = (byte)((playback_pos >> 8) & 0xFF);
            m_DataProtocol.m_DataFrame.data_buf[6] = (byte)((playback_pos >> 16) & 0xFF);
            m_DataProtocol.m_DataFrame.data_buf[7] = (byte)((playback_pos >> 24) & 0xFF);

            //            if (m_DataProtocol.CheckStatus() == PROTO_STAT.IDLE_STAT)
            {
                m_DataProtocol.SendDataFrame(cmd, cmd_param, m_DataProtocol.m_DataFrame.data_buf, Marshal.SizeOf(m_DataProtocol.m_PlaybackSetData));
                ///                m_DataProtocol.SetProtocoStatus(PROTO_STAT.WAIT_FOR_ACK);
            }
        }


        private void SendReadyTrackSetFrameData(int data_idx, byte value)
        {
            ushort cmd;
            byte cmd_type = DataProtocol.CMD_SET_NOTI;
            ushort cmd_param = DataProtocol.CMD_READY_TRACK_SET;
            cmd = m_DataProtocol.GenerateCMD(cmd_type, m_Sender, m_Receiver);

            m_DataProtocol.m_ReadyTrackSetData[data_idx] = value;

            if (m_DataProtocol.CheckStatus() == PROTO_STAT.IDLE_STAT)
            {
                m_DataProtocol.SendDataFrame(cmd, cmd_param, m_DataProtocol.m_ReadyTrackSetData, (int)READY_TRACK_SET.MAX_READY_TRACK_SET);
                //m_DataProtocol.SetProtocoStatus(PROTO_STAT.WAIT_FOR_ACK);
            }
        }

        private void SendControllerSetFrameData(int data_idx, byte value)
        {
            ushort cmd;
            byte cmd_type = DataProtocol.CMD_SET_NOTI;
            ushort cmd_param = DataProtocol.CMD_CONTROLLER_SET;
            cmd = m_DataProtocol.GenerateCMD(cmd_type, m_Sender, m_Receiver);

            m_DataProtocol.m_ControllerSetData[data_idx] = value;

            if (m_DataProtocol.CheckStatus() == PROTO_STAT.IDLE_STAT)
            {
                m_DataProtocol.SendDataFrame(cmd, cmd_param, m_DataProtocol.m_ControllerSetData, (int)CONTROLLER_SET.MAX_CONTROLLER_SET);
                //m_DataProtocol.SetProtocoStatus(PROTO_STAT.WAIT_FOR_ACK);
            }
        }


        private void SendDeleteTrackSetFrameData(int data_idx, byte value)
        {
            ushort cmd;
            byte cmd_type = DataProtocol.CMD_SET_NOTI;
            ushort cmd_param = DataProtocol.CMD_DELETE_TRACK_SET;
            cmd = m_DataProtocol.GenerateCMD(cmd_type, m_Sender, m_Receiver);

            m_DataProtocol.m_DeleteTrackSetData[data_idx] = value;

            if (m_DataProtocol.CheckStatus() == PROTO_STAT.IDLE_STAT)
            {
                m_DataProtocol.SendDataFrame(cmd, cmd_param, m_DataProtocol.m_DeleteTrackSetData, (int)DELETE_TRACK_SET.MAX_DELETE_TRACK_SET);
                //m_DataProtocol.SetProtocoStatus(PROTO_STAT.WAIT_FOR_ACK);
            }
        }

        private void SendBBSetFrameData(int data_idx, byte value)
        {
            ushort cmd;
            byte cmd_type = DataProtocol.CMD_SET_NOTI;
            ushort cmd_param = DataProtocol.CMD_BB_SET;
            cmd = m_DataProtocol.GenerateCMD(cmd_type, m_Sender, m_Receiver);

            m_DataProtocol.m_BaseBandSetData[data_idx] = value;

            if (m_DataProtocol.CheckStatus() == PROTO_STAT.IDLE_STAT)
            {
                m_DataProtocol.SendDataFrame(cmd, cmd_param, m_DataProtocol.m_BaseBandSetData, (int)BB_SET.MAX_BB_SET);
                //m_DataProtocol.SetProtocoStatus(PROTO_STAT.WAIT_FOR_ACK);
            }
        }

        private void SendRF_DdsFreqData()
        {
            ushort cmd;
            byte cmd_type = DataProtocol.CMD_SET_NOTI;
            ushort cmd_param = DataProtocol.CMD_RF_SET;
            cmd = m_DataProtocol.GenerateCMD(cmd_type, m_Sender, m_Receiver);

            m_DataProtocol.m_DataFrame.data_buf[0] = m_DataProtocol.m_SetRfData.PwrCnt;
            m_DataProtocol.m_DataFrame.data_buf[1] = m_DataProtocol.m_SetRfData.DdsAdj;
            m_DataProtocol.m_DataFrame.data_buf[2] = m_DataProtocol.m_SetRfData.DcOffset;
            m_DataProtocol.m_DataFrame.data_buf[3] = m_DataProtocol.m_SetRfData.Reserved1;
            m_DataProtocol.m_DataFrame.data_buf[4] = (byte)(m_DataProtocol.m_SetRfData.MainRxPower & 0xFF);
            m_DataProtocol.m_DataFrame.data_buf[5] = (byte)((m_DataProtocol.m_SetRfData.MainRxPower >> 8) & 0xFF);
            m_DataProtocol.m_DataFrame.data_buf[6] = (byte)(m_DataProtocol.m_SetRfData.MainTxPower & 0xFF);
            m_DataProtocol.m_DataFrame.data_buf[7] = (byte)((m_DataProtocol.m_SetRfData.MainTxPower >> 8) & 0xFF);
            m_DataProtocol.m_DataFrame.data_buf[8] = (byte)(m_DataProtocol.m_SetRfData.WkrRxPower & 0xFF);
            m_DataProtocol.m_DataFrame.data_buf[9] = (byte)((m_DataProtocol.m_SetRfData.WkrRxPower >> 8) & 0xFF);
            m_DataProtocol.m_DataFrame.data_buf[10] = (byte)(m_DataProtocol.m_SetRfData.Reserved3 & 0xFF);
            m_DataProtocol.m_DataFrame.data_buf[11] = (byte)((m_DataProtocol.m_SetRfData.Reserved3 >> 8) & 0xFF);
            m_DataProtocol.m_DataFrame.data_buf[12] = (byte)(m_DataProtocol.m_SetRfData.MainDdsFreq & 0xFF);
            m_DataProtocol.m_DataFrame.data_buf[13] = (byte)((m_DataProtocol.m_SetRfData.MainDdsFreq >> 8) & 0xFF);
            m_DataProtocol.m_DataFrame.data_buf[14] = (byte)((m_DataProtocol.m_SetRfData.MainDdsFreq >> 16) & 0xFF);
            m_DataProtocol.m_DataFrame.data_buf[15] = (byte)((m_DataProtocol.m_SetRfData.MainDdsFreq >> 24) & 0xFF);
            m_DataProtocol.m_DataFrame.data_buf[16] = (byte)(m_DataProtocol.m_SetRfData.WkrDdsFreq & 0xFF);
            m_DataProtocol.m_DataFrame.data_buf[17] = (byte)((m_DataProtocol.m_SetRfData.WkrDdsFreq >> 8) & 0xFF);
            m_DataProtocol.m_DataFrame.data_buf[18] = (byte)((m_DataProtocol.m_SetRfData.WkrDdsFreq >> 16) & 0xFF);
            m_DataProtocol.m_DataFrame.data_buf[19] = (byte)((m_DataProtocol.m_SetRfData.WkrDdsFreq >> 24) & 0xFF);

            //if (m_DataProtocol.CheckStatus() == PROTO_STAT.IDLE_STAT)
            {
                m_DataProtocol.SendDataFrame(cmd, cmd_param, m_DataProtocol.m_DataFrame.data_buf, Marshal.SizeOf(m_DataProtocol.m_SetRfData));
                //m_DataProtocol.SetProtocoStatus(PROTO_STAT.WAIT_FOR_ACK);
            }
        }

        // REQ 
        private void SendReqHeaderOnly(ushort cmd_param)
        {
            ushort cmd;
            byte cmd_type = DataProtocol.CMD_REQ;
            cmd = m_DataProtocol.GenerateCMD(cmd_type, m_Sender, m_Receiver);

            if (m_DataProtocol.CheckStatus() == PROTO_STAT.IDLE_STAT)
                m_DataProtocol.SendDataFrame(cmd, cmd_param, null, 0);
        }

        private void ResetAudioTrackWidget(bool isOn)
        {
            if (isOn)
            {
                lb_audiotrack.Items.Clear();
                bt_playback.Enabled = true;
                bt_delete_track.Enabled = true;
            }
            else
            {
                bt_delete_track.Enabled = false;
                bt_playback.Enabled = false;
            }
        }


        private void TickTimerHandler(object sender, EventArgs e)
        {
            ushort cmd;
            byte cmd_type = DataProtocol.CMD_SET_NOTI;
            ushort cmd_param = DataProtocol.CMD_COM_TEST;

            var ts = m_stopwatch.Elapsed;


            m_elapsedTime = $"{ts.Hours}:{ts.Minutes}:{ts.Seconds}.{ts.Milliseconds / 10}";

            cmd = m_DataProtocol.GenerateCMD(cmd_type, m_Sender, m_Receiver);

            m_DataProtocol.m_CommTestData[(int)DataProtocol.COMM_TEST.TEST_VALUE_1] = (byte)m_rand.Next(0, 255);
            m_DataProtocol.m_CommTestData[(int)DataProtocol.COMM_TEST.TEST_VALUE_2] = (byte)m_rand.Next(0, 255);
            m_DataProtocol.m_CommTestData[(int)DataProtocol.COMM_TEST.TEST_VALUE_3] = (byte)m_rand.Next(0, 255);
            m_DataProtocol.m_CommTestData[(int)DataProtocol.COMM_TEST.TEST_VALUE_4] = (byte)m_rand.Next(0, 255);

            //m_DataProtocol.m_CommTestData[(int)DataProtocol.COMM_TEST.TEST_VALUE_1] = (byte)0x55;
            //m_DataProtocol.m_CommTestData[(int)DataProtocol.COMM_TEST.TEST_VALUE_2] = (byte)0xaa;
            //m_DataProtocol.m_CommTestData[(int)DataProtocol.COMM_TEST.TEST_VALUE_3] = (byte)0x55;
            //m_DataProtocol.m_CommTestData[(int)DataProtocol.COMM_TEST.TEST_VALUE_4] = (byte)0xaa;

            if (m_DataProtocol.CheckStatus() == PROTO_STAT.IDLE_STAT)
            {
                m_DataProtocol.SendDataFrame(cmd, cmd_param, m_DataProtocol.m_CommTestData, (int)DataProtocol.COMM_TEST.MAX_COMM_TEST);
                m_DataProtocol.SetProtocoStatus(PROTO_STAT.WAIT_FOR_ACK);

                m_startTimerMili = ts.TotalMilliseconds;
                m_RetryCnt = 0;

            }
            else if (ts.TotalMilliseconds - m_startTimerMili > 100)  // 100 밀리초 동안 ACK_STAT가 오지 않을시 재전송
            {
                m_RetryCnt++;
                m_startTimerMili = ts.TotalMilliseconds;
                m_DataProtocol.RetrySendFrame();
            }

            if (m_RetryCnt >= 3)
            {
                m_RetryCnt = 0;
                m_TimeOutCnt++;
                m_DataProtocol.Init();
                m_startTimerMili = ts.TotalMilliseconds;
            }

            m_HitRate = ((double)m_DataProtocol.m_DataStatCnt.pass_cnt / (double)m_TotalCnt) * 100.0f;

            UpdateDiagComInfo();
            m_TotalCnt++;
        }

        private void BB_ConnectStatus()
        {
            string strHeader = "BASEBAND VER : ";
            if (m_sys_info.trans_connected == true)
            {
                lb_bb_ver.ForeColor = Color.Green;
                lb_bb_ver.Text = strHeader + m_sys_info.trans_ver;
            }
            else
            {
                ResetAudioTrackWidget(false);

                lb_bb_ver.ForeColor = Color.Red;
                lb_bb_ver.Text = strHeader + "-.-.-.-";
            }
            //richtb_Init.AppendText("Get Transciever Version");

        }

        private void SystemInit()
        {

            SendReqHeaderOnly(CMD_REQ_MOD_INFO);
            m_DataProtocol.SetProtocoStatus(PROTO_STAT.WAIT_FOR_RESPONSE);

            while (m_DataProtocol.CheckStatus() != PROTO_STAT.IDLE_STAT)
                Thread.Sleep(1);

            m_sys_info.trans_ver = m_DataProtocol.m_TransVerData[(int)TRANS_VER.MAJOR].ToString() + '.' +
                                   m_DataProtocol.m_TransVerData[(int)TRANS_VER.MINOR].ToString() + '.' +
                                   m_DataProtocol.m_TransVerData[(int)TRANS_VER.REVISION].ToString() + '.' + "rc" +
                                   m_DataProtocol.m_TransVerData[(int)TRANS_VER.RELEASE].ToString();


            float.TryParse(tb_dds_adj.Text, out float dds_adj);
            m_DataProtocol.m_SetRfData.DdsAdj = (byte)Math.Round(dds_adj * 100);
            float.TryParse(tb_wkr_freq.Text, out float wkr_freq);
            m_DataProtocol.m_SetRfData.WkrDdsFreq = (uint)Math.Round(wkr_freq * 1000);
            float.TryParse(tb_dcoffset.Text, out float dc_offset);
            m_DataProtocol.m_SetRfData.DcOffset = (byte)Math.Round((dc_offset + 1.0f) * 100); // offset 1.0f 

            SettingOpMode(); // OpMode
            Thread.Sleep(50);
            SendBBSetFrameData((int)BB_SET.GAIN, m_gain_value); // Setting GAIN
            Thread.Sleep(50);
            SendBBSetFrameData((int)BB_SET.ATT, m_att_value);   // Setting ATT
            Thread.Sleep(50);
            SendBBSetFrameData((int)BB_SET.LIMITER, m_limiter_value); // limiter
            Thread.Sleep(50);

            SendCodecSetFrameData((int)CODEC_SET.IN_AMP_FRONT_MIC, m_front_mk_value);
            Thread.Sleep(50);

            SendCodecSetFrameData((int)CODEC_SET.IN_AMP_REAR_MIC, m_rear_mk_value);
            Thread.Sleep(50);

            SendCodecSetFrameData((int)CODEC_SET.IN_AMP_WING_MIC, m_wing_mk_value);
            Thread.Sleep(50);

            SendCodecSetFrameData((int)CODEC_SET.IN_AMP_RX_BB, m_rx_bb_value);
            Thread.Sleep(50);

            SendVolSetFrameData((byte)VOL_CH.FRONT_H_SPK, (int)VOL_SET.OUT_FRONT_H_SPK, (byte)(0x18 - m_front_h_spk_value));
            Thread.Sleep(50);

            SendVolSetFrameData((byte)VOL_CH.REAR_WING_H_SPK, (int)VOL_SET.OUT_REAR_WING_SPK, (byte)(0x18 - m_rear_h_spk_value));
            Thread.Sleep(50);

            SendVolSetFrameData((byte)VOL_CH.FRONT_EXT_SPK, (int)VOL_SET.OUT_FRONT_EXT_SPK, (byte)(0x18 - m_rear_wing_h_spk_value));
            Thread.Sleep(50);

            SendVolSetFrameData((byte)VOL_CH.OUT_RX_BB, (int)VOL_SET.OUT_RX_BB, (byte)(0x18 - m_rx_bb_out_value));

        }

        // main thread
        private void MainTaskTread()
        {
            while (true)
            {
                if (m_sys_info.trans_connected == true)
                {
                    if (m_sys_info.system_init == false)
                    {
                        SystemInit();
                        m_sys_info.system_init = true;
                    }
                }
                else
                {
                    m_sys_info.system_init = false;
                }
                Invoke(ConnectStatus);
                Thread.Sleep(1);
            }
        }

        private void TimerConnect(object sender, EventArgs e)
        {

            if (m_DataProtocol.m_ConnectStat == false)
            {
                TabPageOnOff(false);

                timer_connect.Interval = 1000; // 1초로 변경
                m_sys_info.trans_connected = false;
                btnSetController.Enabled = false;
            }
            else
            {
                TabPageOnOff(true);

                timer_connect.Interval = 10000; // 10초로 변경
                m_sys_info.trans_connected = true;
                btnSetController.Enabled = true;
            }

            m_DataProtocol.m_ConnectStat = false;
            SendReqHeaderOnly(CMD_TRANS_CONNECT);            
            Thread.Sleep(100);

        }

        private void tabcontrol_SelectedIndexChanged(object sender, EventArgs e)
        {
            int selectedIndex = tabControl1.SelectedIndex;

            if (m_DataProtocol != null)
            {
                switch (selectedIndex)
                {
                    case 1: // diagnosis
                        m_DataProtocol.ClearStatCnt();
                        break;
                    case 4:
                        rich_tb_full_sentence.Clear();
                        break;
                }
            }
        }

        private void AddReceiveDistressMsgDGV()
        {
            string strDate = DateTime.Now.ToString("HH:mm:ss.fff");
            string format = m_DscMessage.ConvertNumToFormatString(m_DataProtocol.m_DstAlert.Format);
            string addr = "N/A";

            string category = "N/A";

            string self_Id = string.Empty;

            for (int i = 0; i < m_DataProtocol.m_DstAlert.SelfID.Length; i++)
            {
                if (i == (m_DataProtocol.m_DstAlert.SelfID.Length - 1))
                {

                    byte last_data = m_DataProtocol.m_DstAlert.SelfID[i];
                    self_Id += (m_DataProtocol.m_DstAlert.SelfID[i]).ToString()[0].ToString();
                }
                else
                {
                    self_Id += m_DataProtocol.m_DstAlert.SelfID[i].ToString("D2");
                }

            }

            string tcom1 = "N/A";
            string tcom2 = "N/A";
            string freq = "N/A";
            string distid = "N/A";
            string nature = m_DscMessage.ConvertNumToNatureString(m_DataProtocol.m_DstAlert.Nature);

            string pos = string.Empty;

            for (int i = 0; i < m_DataProtocol.m_DstAlert.DstPos.Length; i++)
            {
                pos += m_DataProtocol.m_DstAlert.DstPos[i].ToString("D2");
            }

            string utc = string.Empty;

            for (int i = 0; i < m_DataProtocol.m_DstAlert.UTC.Length; i++)
            {
                utc += m_DataProtocol.m_DstAlert.UTC[i].ToString("D2");
            }

            string expn_pos = string.Empty;

            for (int i = 0; i < m_DataProtocol.m_DstAlert.ExpPos.Length; i++)
            {
                expn_pos += m_DataProtocol.m_DstAlert.ExpPos[i].ToString("D2");
            }

            string eos = m_DscMessage.ConvertNumToEosString(m_DataProtocol.m_DstAlert.EOS);

            int rowIndex = dgvDscRsv.Rows.Add(strDate, format, addr, category, self_Id, tcom1, tcom2, freq, distid, nature, pos, utc, expn_pos, eos);
            dgvDscRsv.Rows[rowIndex].Cells["DscAddr"].Style.BackColor = Color.LightGray;
            dgvDscRsv.Rows[rowIndex].Cells["DscCategory"].Style.BackColor = Color.LightGray;
            dgvDscRsv.Rows[rowIndex].Cells["DscTeleCom1"].Style.BackColor = Color.LightGray;
            dgvDscRsv.Rows[rowIndex].Cells["DscTeleCom2"].Style.BackColor = Color.LightGray;
            dgvDscRsv.Rows[rowIndex].Cells["DscFreq"].Style.BackColor = Color.LightGray;
            dgvDscRsv.Rows[rowIndex].Cells["DscDistID"].Style.BackColor = Color.LightGray;

            if (tabControl1.SelectedTab == tabPage3)
                dgvDscRsv.FirstDisplayedScrollingRowIndex = rowIndex++;

            lb_rx_dsc.Text = "RX DSC (" + rowIndex.ToString() + ")";
        }

        private void AddReceiveDistressAckMsgDGV()
        {
            string strDate = DateTime.Now.ToString("HH:mm:ss.fff");
            string format = m_DscMessage.ConvertNumToFormatString(m_DataProtocol.m_DstAlertAck.Format);
            string addr = "N/A";

            string category = m_DscMessage.ConvertNumToCategoryString(m_DataProtocol.m_DstAlertAck.Category);

            string self_Id = string.Empty;

            for (int i = 0; i < m_DataProtocol.m_DstAlertAck.SelfID.Length; i++)
            {
                if (i == (m_DataProtocol.m_DstAlertAck.SelfID.Length - 1))
                {

                    byte last_data = m_DataProtocol.m_DstAlertAck.SelfID[i];
                    self_Id += (m_DataProtocol.m_DstAlertAck.SelfID[i]).ToString()[0].ToString();
                }
                else
                {
                    self_Id += m_DataProtocol.m_DstAlertAck.SelfID[i].ToString("D2");
                }
            }

            string tcom1 = m_DscMessage.ConvertNumToTelCom1String(m_DataProtocol.m_DstAlertAck.TelCmd0); ;
            string tcom2 = "N/A";
            string freq = "N/A";
            string distid = string.Empty;

            for (int i = 0; i < m_DataProtocol.m_DstAlertAck.DistID.Length; i++)
            {
                if (i == (m_DataProtocol.m_DstAlertAck.DistID.Length - 1))
                {

                    byte last_data = m_DataProtocol.m_DstAlertAck.DistID[i];
                    distid += (m_DataProtocol.m_DstAlertAck.DistID[i]).ToString()[0].ToString();
                }
                else
                {
                    distid += m_DataProtocol.m_DstAlertAck.DistID[i].ToString("D2");
                }
            }

            string nature = m_DscMessage.ConvertNumToNatureString(m_DataProtocol.m_DstAlertAck.Nature);

            string pos = string.Empty;

            for (int i = 0; i < m_DataProtocol.m_DstAlertAck.DstPos.Length; i++)
            {
                pos += m_DataProtocol.m_DstAlertAck.DstPos[i].ToString("D2");
            }

            string utc = string.Empty;

            for (int i = 0; i < m_DataProtocol.m_DstAlertAck.UTC.Length; i++)
            {
                utc += m_DataProtocol.m_DstAlertAck.UTC[i].ToString("D2");
            }

            string expn_pos = string.Empty;

            for (int i = 0; i < m_DataProtocol.m_DstAlertAck.ExpPos.Length; i++)
            {
                expn_pos += m_DataProtocol.m_DstAlertAck.ExpPos[i].ToString("D2");
            }

            string eos = m_DscMessage.ConvertNumToEosString(m_DataProtocol.m_DstAlertAck.EOS);

            int rowIndex = dgvDscRsv.Rows.Add(strDate, format, addr, category, self_Id, tcom1, tcom2, freq, distid, nature, pos, utc, expn_pos, eos);
            dgvDscRsv.Rows[rowIndex].Cells["DscAddr"].Style.BackColor = Color.LightGray;
            dgvDscRsv.Rows[rowIndex].Cells["DscTeleCom2"].Style.BackColor = Color.LightGray;
            dgvDscRsv.Rows[rowIndex].Cells["DscFreq"].Style.BackColor = Color.LightGray;

            if (tabControl1.SelectedTab == tabPage3)
                dgvDscRsv.FirstDisplayedScrollingRowIndex = rowIndex++;

            lb_rx_dsc.Text = "RX DSC (" + rowIndex.ToString() + ")";
        }

        private void AddReceiveDistressRelayMsgDGV()
        {
            string strDate = DateTime.Now.ToString("HH:mm:ss.fff");
            string format = m_DscMessage.ConvertNumToFormatString(m_DataProtocol.m_DstAlertRelay.Format);

            string addr = string.Empty;
            if (format.Equals(DscMessage.FMT_116))
            {
                addr = "N/A";
            }
            else if (format.Equals(DscMessage.FMT_102))
            {
                for (int i = 0; i < m_DataProtocol.m_DstAlertRelay.AddrID.Length; i++)
                {
                    addr += m_DataProtocol.m_DstAlertRelay.AddrID[i].ToString("D2");
                }
            }
            else
            {
                for (int i = 0; i < m_DataProtocol.m_DstAlertRelay.AddrID.Length; i++)
                {
                    if (i == (m_DataProtocol.m_DstAlertRelay.AddrID.Length - 1))
                    {

                        byte last_data = m_DataProtocol.m_DstAlertRelay.AddrID[i];
                        addr += (m_DataProtocol.m_DstAlertRelay.AddrID[i]).ToString()[0].ToString();
                    }
                    else
                    {
                        addr += m_DataProtocol.m_DstAlertRelay.AddrID[i].ToString("D2");
                    }
                }

            }

            string category = m_DscMessage.ConvertNumToCategoryString(m_DataProtocol.m_DstAlertRelay.Category);

            string self_Id = string.Empty;

            for (int i = 0; i < m_DataProtocol.m_DstAlertRelay.SelfID.Length; i++)
            {
                if (i == (m_DataProtocol.m_DstAlertRelay.SelfID.Length - 1))
                {

                    byte last_data = m_DataProtocol.m_DstAlertRelay.SelfID[i];
                    self_Id += (m_DataProtocol.m_DstAlertRelay.SelfID[i]).ToString()[0].ToString();
                }
                else
                {
                    self_Id += m_DataProtocol.m_DstAlertRelay.SelfID[i].ToString("D2");
                }
            }

            string tcom1 = m_DscMessage.ConvertNumToTelCom1String(m_DataProtocol.m_DstAlertRelay.TelCmd0); ;
            string tcom2 = "N/A";
            string freq = "N/A";
            string distid = string.Empty;

            for (int i = 0; i < m_DataProtocol.m_DstAlertRelay.DistID.Length; i++)
            {
                if (i == (m_DataProtocol.m_DstAlertRelay.DistID.Length - 1))
                {

                    byte last_data = m_DataProtocol.m_DstAlertRelay.DistID[i];
                    distid += (m_DataProtocol.m_DstAlertRelay.DistID[i]).ToString()[0].ToString();
                }
                else
                {
                    distid += m_DataProtocol.m_DstAlertRelay.DistID[i].ToString("D2");
                }
            }

            string nature = m_DscMessage.ConvertNumToNatureString(m_DataProtocol.m_DstAlertRelay.Nature);

            string pos = string.Empty;

            for (int i = 0; i < m_DataProtocol.m_DstAlertRelay.DstPos.Length; i++)
            {
                pos += m_DataProtocol.m_DstAlertRelay.DstPos[i].ToString("D2");
            }

            string utc = string.Empty;

            for (int i = 0; i < m_DataProtocol.m_DstAlertRelay.UTC.Length; i++)
            {
                utc += m_DataProtocol.m_DstAlertRelay.UTC[i].ToString("D2");
            }

            string expn_pos = string.Empty;

            for (int i = 0; i < m_DataProtocol.m_DstAlertRelay.ExpPos.Length; i++)
            {
                expn_pos += m_DataProtocol.m_DstAlertRelay.ExpPos[i].ToString("D2");
            }

            string eos = m_DscMessage.ConvertNumToEosString(m_DataProtocol.m_DstAlertRelay.EOS);

            int rowIndex = dgvDscRsv.Rows.Add(strDate, format, addr, category, self_Id, tcom1, tcom2, freq, distid, nature, pos, utc, expn_pos, eos);
            dgvDscRsv.Rows[rowIndex].Cells["DscTeleCom2"].Style.BackColor = Color.LightGray;
            dgvDscRsv.Rows[rowIndex].Cells["DscFreq"].Style.BackColor = Color.LightGray;

            if (format.Equals(DscMessage.FMT_116))
                dgvDscRsv.Rows[rowIndex].Cells["DscAddr"].Style.BackColor = Color.LightGray;

            if (tabControl1.SelectedTab == tabPage3)
                dgvDscRsv.FirstDisplayedScrollingRowIndex = rowIndex++;

            lb_rx_dsc.Text = "RX DSC (" + rowIndex.ToString() + ")";
        }

        private void AddReceiveDistressRelayAckMsgDGV()
        {
            string strDate = DateTime.Now.ToString("HH:mm:ss.fff");
            string format = m_DscMessage.ConvertNumToFormatString(m_DataProtocol.m_DstAlertRelayAck.Format);
            string addr = string.Empty;

            if (format.Equals(DscMessage.FMT_116))
            {
                addr = "N/A";
            }
            else
            {
                for (int i = 0; i < m_DataProtocol.m_DstAlertRelayAck.AddrID.Length; i++)
                {
                    if (i == (m_DataProtocol.m_DstAlertRelayAck.AddrID.Length - 1))
                    {

                        byte last_data = m_DataProtocol.m_DstAlertRelayAck.AddrID[i];
                        addr += (m_DataProtocol.m_DstAlertRelayAck.AddrID[i]).ToString()[0].ToString();
                    }
                    else
                    {
                        addr += m_DataProtocol.m_DstAlertRelayAck.AddrID[i].ToString("D2");
                    }
                }
            }

            string category = m_DscMessage.ConvertNumToCategoryString(m_DataProtocol.m_DstAlertRelayAck.Category);

            string self_Id = string.Empty;

            for (int i = 0; i < m_DataProtocol.m_DstAlertRelayAck.SelfID.Length; i++)
            {
                if (i == (m_DataProtocol.m_DstAlertRelayAck.SelfID.Length - 1))
                {

                    byte last_data = m_DataProtocol.m_DstAlertRelayAck.SelfID[i];
                    self_Id += (m_DataProtocol.m_DstAlertRelayAck.SelfID[i]).ToString()[0].ToString();
                }
                else
                {
                    self_Id += m_DataProtocol.m_DstAlertRelayAck.SelfID[i].ToString("D2");
                }
            }

            string tcom1 = m_DscMessage.ConvertNumToTelCom1String(m_DataProtocol.m_DstAlertRelayAck.TelCmd0); ;
            string tcom2 = "N/A";
            string freq = "N/A";
            string distid = string.Empty;

            for (int i = 0; i < m_DataProtocol.m_DstAlertRelayAck.DistID.Length; i++)
            {
                if (i == (m_DataProtocol.m_DstAlertRelayAck.DistID.Length - 1))
                {

                    byte last_data = m_DataProtocol.m_DstAlertRelayAck.DistID[i];
                    distid += (m_DataProtocol.m_DstAlertRelayAck.DistID[i]).ToString()[0].ToString();
                }
                else
                {
                    distid += m_DataProtocol.m_DstAlertRelayAck.DistID[i].ToString("D2");
                }
            }

            string nature = m_DscMessage.ConvertNumToNatureString(m_DataProtocol.m_DstAlertRelayAck.Nature);

            string pos = string.Empty;

            for (int i = 0; i < m_DataProtocol.m_DstAlertRelayAck.DstPos.Length; i++)
            {
                pos += m_DataProtocol.m_DstAlertRelayAck.DstPos[i].ToString("D2");
            }

            string utc = string.Empty;

            for (int i = 0; i < m_DataProtocol.m_DstAlertRelayAck.UTC.Length; i++)
            {
                utc += m_DataProtocol.m_DstAlertRelayAck.UTC[i].ToString("D2");
            }

            string expn_pos = string.Empty;

            for (int i = 0; i < m_DataProtocol.m_DstAlertRelayAck.ExpPos.Length; i++)
            {
                expn_pos += m_DataProtocol.m_DstAlertRelayAck.ExpPos[i].ToString("D2");
            }

            string eos = m_DscMessage.ConvertNumToEosString(m_DataProtocol.m_DstAlertRelay.EOS);

            int rowIndex = dgvDscRsv.Rows.Add(strDate, format, addr, category, self_Id, tcom1, tcom2, freq, distid, nature, pos, utc, expn_pos, eos);
            dgvDscRsv.Rows[rowIndex].Cells["DscTeleCom2"].Style.BackColor = Color.LightGray;
            dgvDscRsv.Rows[rowIndex].Cells["DscFreq"].Style.BackColor = Color.LightGray;

            if (format.Equals(DscMessage.FMT_116))
                dgvDscRsv.Rows[rowIndex].Cells["DscAddr"].Style.BackColor = Color.LightGray;

            if (tabControl1.SelectedTab == tabPage3)
                dgvDscRsv.FirstDisplayedScrollingRowIndex = rowIndex++;

            lb_rx_dsc.Text = "RX DSC (" + rowIndex.ToString() + ")";
        }

        private void AddReceiveAllShipMsgDGV()
        {
            string strDate = DateTime.Now.ToString("HH:mm:ss.fff");
            string format = m_DscMessage.ConvertNumToFormatString(m_DataProtocol.m_AllShip.Format);
            string addr = "N/A";

            string category = m_DscMessage.ConvertNumToCategoryString(m_DataProtocol.m_AllShip.Category);

            string self_Id = string.Empty;

            for (int i = 0; i < m_DataProtocol.m_AllShip.SelfID.Length; i++)
            {
                if (i == (m_DataProtocol.m_AllShip.SelfID.Length - 1))
                {

                    byte last_data = m_DataProtocol.m_AllShip.SelfID[i];
                    self_Id += (m_DataProtocol.m_AllShip.SelfID[i]).ToString()[0].ToString();
                }
                else
                {
                    self_Id += m_DataProtocol.m_AllShip.SelfID[i].ToString("D2");
                }
            }

            string tcom1 = m_DscMessage.ConvertNumToTelCom1String(m_DataProtocol.m_AllShip.TelCmd0);
            string tcom2 = m_DscMessage.ConvertNumToTelCom2String(m_DataProtocol.m_AllShip.TelCmd1);
            string freq = string.Empty;

            for (int i = 0; i < m_DataProtocol.m_AllShip.Freq.Length - 2; i++)
            {
                freq += m_DataProtocol.m_AllShip.Freq[i].ToString("D2");
            }

            string distid = "N/A";

            string nature = "N/A";

            string pos = "N/A";

            string utc = "N/A";

            string expn_pos = "N/A";

            string eos = m_DscMessage.ConvertNumToEosString(m_DataProtocol.m_AllShip.EOS);

            int rowIndex = dgvDscRsv.Rows.Add(strDate, format, addr, category, self_Id, tcom1, tcom2, freq, distid, nature, pos, utc, expn_pos, eos);
            dgvDscRsv.Rows[rowIndex].Cells["DscAddr"].Style.BackColor = Color.LightGray;
            dgvDscRsv.Rows[rowIndex].Cells["DscDistID"].Style.BackColor = Color.LightGray;
            dgvDscRsv.Rows[rowIndex].Cells["DscNature"].Style.BackColor = Color.LightGray;
            dgvDscRsv.Rows[rowIndex].Cells["DscPos"].Style.BackColor = Color.LightGray;
            dgvDscRsv.Rows[rowIndex].Cells["DscUTC"].Style.BackColor = Color.LightGray;
            dgvDscRsv.Rows[rowIndex].Cells["DscExpnPos"].Style.BackColor = Color.LightGray;

            if (tabControl1.SelectedTab == tabPage3)
                dgvDscRsv.FirstDisplayedScrollingRowIndex = rowIndex++;

            lb_rx_dsc.Text = "RX DSC (" + rowIndex.ToString() + ")";
        }

        private void AddReceiveGrpCallMsgDGV()
        {
            string strDate = DateTime.Now.ToString("HH:mm:ss.fff");
            string format = m_DscMessage.ConvertNumToFormatString(m_DataProtocol.m_GrpCall.Format);
            string addr = string.Empty;

            for (int i = 0; i < m_DataProtocol.m_GrpCall.AddrID.Length; i++)
            {
                if (i == (m_DataProtocol.m_GrpCall.AddrID.Length - 1))
                {

                    byte last_data = m_DataProtocol.m_GrpCall.AddrID[i];
                    addr += (m_DataProtocol.m_GrpCall.AddrID[i]).ToString()[0].ToString();
                }
                else
                {
                    addr += m_DataProtocol.m_GrpCall.AddrID[i].ToString("D2");
                }
            }

            string category = m_DscMessage.ConvertNumToCategoryString(m_DataProtocol.m_GrpCall.Category);

            string self_Id = string.Empty;

            for (int i = 0; i < m_DataProtocol.m_GrpCall.SelfID.Length; i++)
            {
                if (i == (m_DataProtocol.m_GrpCall.SelfID.Length - 1))
                {

                    byte last_data = m_DataProtocol.m_GrpCall.SelfID[i];
                    self_Id += (m_DataProtocol.m_GrpCall.SelfID[i]).ToString()[0].ToString();
                }
                else
                {
                    self_Id += m_DataProtocol.m_GrpCall.SelfID[i].ToString("D2");
                }
            }

            string tcom1 = m_DscMessage.ConvertNumToTelCom1String(m_DataProtocol.m_GrpCall.TelCmd0);
            string tcom2 = m_DscMessage.ConvertNumToTelCom2String(m_DataProtocol.m_GrpCall.TelCmd1);
            string freq = string.Empty;

            for (int i = 0; i < m_DataProtocol.m_GeoAREA.Freq.Length - 2; i++)
            {
                freq += m_DataProtocol.m_GrpCall.Freq[i].ToString("D2");
            }

            string distid = "N/A";
            string nature = "N/A";
            string pos = "N/A";
            string utc = "N/A";
            string expn_pos = "N/A";

            string eos = m_DscMessage.ConvertNumToEosString(m_DataProtocol.m_GrpCall.EOS);

            int rowIndex = dgvDscRsv.Rows.Add(strDate, format, addr, category, self_Id, tcom1, tcom2, freq, distid, nature, pos, utc, expn_pos, eos);
            dgvDscRsv.Rows[rowIndex].Cells["DscDistID"].Style.BackColor = Color.LightGray;
            dgvDscRsv.Rows[rowIndex].Cells["DscNature"].Style.BackColor = Color.LightGray;
            dgvDscRsv.Rows[rowIndex].Cells["DscPos"].Style.BackColor = Color.LightGray;
            dgvDscRsv.Rows[rowIndex].Cells["DscUTC"].Style.BackColor = Color.LightGray;
            dgvDscRsv.Rows[rowIndex].Cells["DscExpnPos"].Style.BackColor = Color.LightGray;

            if (tabControl1.SelectedTab == tabPage3)
                dgvDscRsv.FirstDisplayedScrollingRowIndex = rowIndex;

            lb_rx_dsc.Text = "RX DSC (" + rowIndex.ToString() + ")";
        }

        private void AddReceiveGeoAreaMsgDGV()
        {
            string strDate = DateTime.Now.ToString("HH:mm:ss.fff");
            string format = m_DscMessage.ConvertNumToFormatString(m_DataProtocol.m_GeoAREA.Format);
            string addr = string.Empty;

            for (int i = 0; i < m_DataProtocol.m_GeoAREA.AddrID.Length; i++)
            {
                if (i == (m_DataProtocol.m_GeoAREA.AddrID.Length - 1))
                {

                    byte last_data = m_DataProtocol.m_GeoAREA.AddrID[i];
                    addr += (m_DataProtocol.m_GeoAREA.AddrID[i]).ToString()[0].ToString();
                }
                else
                {
                    addr += m_DataProtocol.m_GeoAREA.AddrID[i].ToString("D2");
                }
            }

            string category = m_DscMessage.ConvertNumToCategoryString(m_DataProtocol.m_GeoAREA.Category);

            string self_Id = string.Empty;

            for (int i = 0; i < m_DataProtocol.m_GeoAREA.SelfID.Length; i++)
            {
                if (i == (m_DataProtocol.m_GeoAREA.SelfID.Length - 1))
                {

                    byte last_data = m_DataProtocol.m_GeoAREA.SelfID[i];
                    self_Id += (m_DataProtocol.m_GeoAREA.SelfID[i]).ToString()[0].ToString();
                }
                else
                {
                    self_Id += m_DataProtocol.m_GeoAREA.SelfID[i].ToString("D2");
                }
            }

            string tcom1 = m_DscMessage.ConvertNumToTelCom1String(m_DataProtocol.m_GeoAREA.TelCmd0);
            string tcom2 = m_DscMessage.ConvertNumToTelCom1String(m_DataProtocol.m_GeoAREA.TelCmd1);
            string freq = string.Empty;

            for (int i = 0; i < m_DataProtocol.m_GeoAREA.Freq.Length - 2; i++)
            {
                freq += m_DataProtocol.m_GeoAREA.Freq[i].ToString("D2");
            }

            string distid = "N/A";
            string nature = "N/A";
            string pos = "N/A";
            string utc = "N/A"; ;
            string expn_pos = "N/A";

            string eos = m_DscMessage.ConvertNumToEosString(m_DataProtocol.m_GeoAREA.EOS);

            int rowIndex = dgvDscRsv.Rows.Add(strDate, format, addr, category, self_Id, tcom1, tcom2, freq, distid, nature, pos, utc, expn_pos, eos);

            dgvDscRsv.Rows[rowIndex].Cells["DscDistID"].Style.BackColor = Color.LightGray;
            dgvDscRsv.Rows[rowIndex].Cells["DscNature"].Style.BackColor = Color.LightGray;
            dgvDscRsv.Rows[rowIndex].Cells["DscPos"].Style.BackColor = Color.LightGray;
            dgvDscRsv.Rows[rowIndex].Cells["DscUTC"].Style.BackColor = Color.LightGray;
            dgvDscRsv.Rows[rowIndex].Cells["DscExpnPos"].Style.BackColor = Color.LightGray;

            if (tabControl1.SelectedTab == tabPage3)
                dgvDscRsv.FirstDisplayedScrollingRowIndex = rowIndex++;

            lb_rx_dsc.Text = "RX DSC (" + rowIndex.ToString() + ")";
        }

        private void AddReceiveIndiCallMsgDGV()
        {
            string strDate = DateTime.Now.ToString("HH:mm:ss.fff");
            string format = m_DscMessage.ConvertNumToFormatString(m_DataProtocol.m_IndiCall.Format);
            string addr = string.Empty;

            for (int i = 0; i < m_DataProtocol.m_IndiCall.AddrID.Length; i++)
            {
                if (i == (m_DataProtocol.m_IndiCall.AddrID.Length - 1))
                {

                    byte last_data = m_DataProtocol.m_IndiCall.AddrID[i];
                    addr += (m_DataProtocol.m_IndiCall.AddrID[i]).ToString()[0].ToString();
                }
                else
                {
                    addr += m_DataProtocol.m_IndiCall.AddrID[i].ToString("D2");
                }
            }

            string category = m_DscMessage.ConvertNumToCategoryString(m_DataProtocol.m_IndiCall.Category);

            string self_Id = string.Empty;

            for (int i = 0; i < m_DataProtocol.m_IndiCall.SelfID.Length; i++)
            {
                if (i == (m_DataProtocol.m_IndiCall.SelfID.Length - 1))
                {

                    byte last_data = m_DataProtocol.m_IndiCall.SelfID[i];
                    self_Id += (m_DataProtocol.m_IndiCall.SelfID[i]).ToString()[0].ToString();
                }
                else
                {
                    self_Id += m_DataProtocol.m_IndiCall.SelfID[i].ToString("D2");
                }
            }

            string tcom1 = m_DscMessage.ConvertNumToTelCom1String(m_DataProtocol.m_IndiCall.TelCmd0);
            string tcom2 = m_DscMessage.ConvertNumToTelCom2String(m_DataProtocol.m_IndiCall.TelCmd1);

            string distid = "N/A";
            string nature = "N/A";

            string freq = string.Empty;

            for (int i = 0; i < m_DataProtocol.m_IndiCall.Freq.Length - 2; i++)
            {
                freq += m_DataProtocol.m_IndiCall.Freq[i].ToString("D2");
            }

            string pos = "N/A";
            string utc = string.Empty;
            string expn_pos = string.Empty;

            if (tcom1.Equals(DscMessage.TELCOM1_121) && m_DataProtocol.m_IndiCall.EOS == 122)  // POSTION ACK
            {
                for (int i = 0; i < m_DataProtocol.m_IndiCall.UTC.Length; i++)
                {
                    utc += m_DataProtocol.m_IndiCall.UTC[i].ToString("D2");
                }

                for (int i = 0; i < m_DataProtocol.m_IndiCall.ExpPos.Length; i++)
                {
                    expn_pos += m_DataProtocol.m_IndiCall.ExpPos[i].ToString("D2");
                }

            }
            else
            {
                utc = "N/A";
                expn_pos = "N/A";
            }

            string eos = m_DscMessage.ConvertNumToEosString(m_DataProtocol.m_IndiCall.EOS);

            int rowIndex = dgvDscRsv.Rows.Add(strDate, format, addr, category, self_Id, tcom1, tcom2, freq, distid, nature, pos, utc, expn_pos, eos);

            dgvDscRsv.Rows[rowIndex].Cells["DscDistID"].Style.BackColor = Color.LightGray;
            dgvDscRsv.Rows[rowIndex].Cells["DscNature"].Style.BackColor = Color.LightGray;
            dgvDscRsv.Rows[rowIndex].Cells["DscPos"].Style.BackColor = Color.LightGray;

            if (tcom1.Equals(DscMessage.TELCOM1_121) == false)
            {
                dgvDscRsv.Rows[rowIndex].Cells["DscUTC"].Style.BackColor = Color.LightGray;
                dgvDscRsv.Rows[rowIndex].Cells["DscExpnPos"].Style.BackColor = Color.LightGray;
            }

            if (tabControl1.SelectedTab == tabPage3)
                dgvDscRsv.FirstDisplayedScrollingRowIndex = rowIndex++;

            lb_rx_dsc.Text = "RX DSC (" + rowIndex.ToString() + ")";
        }

        private void AddReceiveSemiAUTOMsgDGV()
        {
            string strDate = DateTime.Now.ToString("HH:mm:ss.fff");
            string format = m_DscMessage.ConvertNumToFormatString(m_DataProtocol.m_SemiAUTO.Format);
            string addr = string.Empty;

            for (int i = 0; i < m_DataProtocol.m_SemiAUTO.AddrID.Length; i++)
            {
                if (i == (m_DataProtocol.m_SemiAUTO.AddrID.Length - 1))
                {

                    byte last_data = m_DataProtocol.m_SemiAUTO.AddrID[i];
                    addr += (m_DataProtocol.m_SemiAUTO.AddrID[i]).ToString()[0].ToString();
                }
                else
                {
                    addr += m_DataProtocol.m_SemiAUTO.AddrID[i].ToString("D2");
                }
            }

            string category = m_DscMessage.ConvertNumToCategoryString(m_DataProtocol.m_SemiAUTO.Category);

            string self_Id = string.Empty;

            for (int i = 0; i < m_DataProtocol.m_SemiAUTO.SelfID.Length; i++)
            {
                if (i == (m_DataProtocol.m_SemiAUTO.SelfID.Length - 1))
                {
                    byte last_data = m_DataProtocol.m_SemiAUTO.SelfID[i];
                    self_Id += (m_DataProtocol.m_SemiAUTO.SelfID[i]).ToString()[0].ToString();
                }
                else
                {
                    self_Id += m_DataProtocol.m_SemiAUTO.SelfID[i].ToString("D2");
                }
            }

            string tcom1 = m_DscMessage.ConvertNumToTelCom1String(m_DataProtocol.m_SemiAUTO.TelCmd0);
            string tcom2 = m_DscMessage.ConvertNumToTelCom2String(m_DataProtocol.m_SemiAUTO.TelCmd1);

            string distid = "N/A";
            string nature = "N/A";

            string freq = string.Empty;

            for (int i = 0; i < m_DataProtocol.m_SemiAUTO.Freq.Length - 5; i++)
            {
                freq += m_DataProtocol.m_SemiAUTO.Freq[i].ToString("D2");
            }

            string TelNum = string.Empty;

            for (int i = 0; i < m_DataProtocol.m_SemiAUTO.TelNo.Length; i++)
            {
                TelNum += m_DataProtocol.m_SemiAUTO.TelNo[i].ToString("D2");
            }

            string utc = "N/A";

            string expn_pos = "N/A";

            string eos = m_DscMessage.ConvertNumToEosString(m_DataProtocol.m_SemiAUTO.EOS);

            int rowIndex = dgvDscRsv.Rows.Add(strDate, format, addr, category, self_Id, tcom1, tcom2, freq, distid, nature, TelNum, utc, expn_pos, eos);

            dgvDscRsv.Rows[rowIndex].Cells["DscDistID"].Style.BackColor = Color.LightGray;
            dgvDscRsv.Rows[rowIndex].Cells["DscNature"].Style.BackColor = Color.LightGray;
            dgvDscRsv.Rows[rowIndex].Cells["DscUTC"].Style.BackColor = Color.LightGray;
            dgvDscRsv.Rows[rowIndex].Cells["DscExpnPos"].Style.BackColor = Color.LightGray;

            if (tabControl1.SelectedTab == tabPage3)
                dgvDscRsv.FirstDisplayedScrollingRowIndex = rowIndex++;

            lb_rx_dsc.Text = "RX DSC (" + rowIndex.ToString() + ")";
        }

        private void FullExtCommSentence()
        {
            string timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            string dataString = Encoding.ASCII.GetString(m_DataProtocol.m_DataFrame.data_buf, 1, m_DataProtocol.m_DataFrame.data_buf.Length - 1);
            if (m_DataProtocol.m_DataFrame.data_buf[0] == 1)
            {
                rich_tb_full_sentence.AppendText(timestamp + " [1]: $" + dataString + "\n");
            }
            else if (m_DataProtocol.m_DataFrame.data_buf[0] == 2)
            {
                rich_tb_full_sentence.SelectionColor = Color.Green;
                rich_tb_full_sentence.AppendText(timestamp + " [2]: $" + dataString + "\n");
                rich_tb_full_sentence.SelectionColor = Color.Black;
            }
            else
            {
                rich_tb_full_sentence.SelectionColor = Color.Blue;
                rich_tb_full_sentence.AppendText(timestamp + " [450]: $" + dataString + "\n");
                rich_tb_full_sentence.SelectionColor = Color.Black;
            }

            rich_tb_full_sentence.ScrollToCaret();

            Array.Clear(m_DataProtocol.m_DataFrame.data_buf, 0, m_DataProtocol.m_DataFrame.data_buf.Length);
        }


        private void UpdatePosTm()
        {

            int latitude = m_DataProtocol.m_PosTime.lat;
            int longitude = m_DataProtocol.m_PosTime.lon;

            tb_date.Text = m_DataProtocol.m_PosTime.date != -1
                ? new DateTime((m_DataProtocol.m_PosTime.date / 10000 < 100 ? 2000 + m_DataProtocol.m_PosTime.date / 10000 : m_DataProtocol.m_PosTime.date / 10000), (m_DataProtocol.m_PosTime.date % 10000) / 100,
                 m_DataProtocol.m_PosTime.date % 100).ToString("yyyy-MM-dd") : "N/A";

            int hours = m_DataProtocol.m_PosTime.time / 10000;
            int mins = (m_DataProtocol.m_PosTime.time - hours * 10000) / 100;
            int secs = m_DataProtocol.m_PosTime.time - hours * 10000 - mins * 100;

            tb_latitude.Text = m_DataProtocol.GetLatStringEx(latitude, DataProtocol.PosStrFmt.Normal);
            tb_logitude.Text = m_DataProtocol.GetLonStringEx(longitude, DataProtocol.PosStrFmt.Normal);
            tb_time.Text = hours.ToString() + ":" + mins.ToString() + ":" + secs.ToString();

            tb_lz_hours.Text = m_DataProtocol.m_PosTime.lz_hours.ToString();
            tb_lz_mins.Text = m_DataProtocol.m_PosTime.lz_mins.ToString();
        }

        private void UpdateAudioTrackInfo()
        {
            lb_audiotrack.Items.Clear();

            for (int i = 0; i < m_DataProtocol.m_TrackInfoData.TrackNum.Length; i++)
            {
                if (m_DataProtocol.m_TrackInfoData.MagicCode[i] == 0x55AA55AA)
                {
                    string timeStampHex = BitConverter.ToString(BitConverter.GetBytes(m_DataProtocol.m_TrackInfoData.TimeStamp[i])).Replace("-", "");
                    string minute = timeStampHex.Substring(0, 2);
                    string hour = timeStampHex.Substring(2, 2);
                    string day = timeStampHex.Substring(4, 2);
                    string month = timeStampHex.Substring(6, 2);
                    string trackInfo = "TRACK" + " [" + m_DataProtocol.m_TrackInfoData.TrackNum[i].ToString() + "] :" + " " +
                    month + "-" + day + " " + hour + ":" + minute
                    + " " + (m_DataProtocol.m_TrackInfoData.RecordMS[i] / 1000.0).ToString("F2") + "s";

                    lb_audiotrack.Items.Add(trackInfo);
                    m_AudioTrackRecordMS[i] = m_DataProtocol.m_TrackInfoData.RecordMS[i];
                }
            }
        }

        private void UpdateRecordDone()
        {
            bt_record.Text = "REC OFF";
            bt_record.BackColor = Color.Transparent;
            bt_record.ForeColor = Color.Black;

            MessageBox.Show("Recording has been completed.", "Notification", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void UpdateSelfTestResult()
        {

            string timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

            string test_item = string.Empty;
            string test_status = string.Empty;

            if (m_DataProtocol.m_SelfTestData.test_item == 1)
            {
                test_item = "DSC LOOP TEST";
            }

            if (m_DataProtocol.m_SelfTestData.test_status == 2)
            {
                test_status = "PASS";
                m_selftest_tot_pass_count++;

                lb_selftest_tot_pass.Text = "PASS : " + m_selftest_tot_pass_count.ToString();
            }
            else if (m_DataProtocol.m_SelfTestData.test_status == 3)
            {

                test_status = "FAIL";
                m_selftest_tot_fail_count++;

                lb_selftest_tot_fail.Text = "FAIL : " + m_selftest_tot_fail_count.ToString();
            }

            int rowIndex = dgv_selftest.Rows.Add(timestamp, test_item, test_status);

            dgv_selftest.Rows[rowIndex].Cells["ResultCol"].Style.ForeColor = Color.White;
            if (m_DataProtocol.m_SelfTestData.test_status == 2)
            {
                dgv_selftest.Rows[rowIndex].Cells["ResultCol"].Style.BackColor = Color.Green;
            }
            else if (m_DataProtocol.m_SelfTestData.test_status == 3)
            {
                dgv_selftest.Rows[rowIndex].Cells["ResultCol"].Style.BackColor = Color.Red;
            }

            if (tabControl1.SelectedTab == tabPage2)
                dgv_selftest.FirstDisplayedScrollingRowIndex = rowIndex++;
        }

        private void DscMsgStatus()
        {
            switch (m_DataProtocol.m_DataFrame.data_buf[0])
            {
                case (byte)MSG_TYPE.DSC_MSG_TYPE_DST_ALT:
                    AddReceiveDistressMsgDGV();
                    break;
                case (byte)MSG_TYPE.DSC_MSG_TYPE_DST_ACK:
                    AddReceiveDistressAckMsgDGV();
                    break;
                case (byte)MSG_TYPE.DSC_MSG_TYPE_DST_RLY:
                    AddReceiveDistressRelayMsgDGV();
                    break;
                case (byte)MSG_TYPE.DSC_MSG_TYPE_RLY_ACK:
                    AddReceiveDistressRelayAckMsgDGV();
                    break;
                case (byte)MSG_TYPE.DSC_MSG_TYPE_ALL_SHIP:
                    AddReceiveAllShipMsgDGV();
                    break;
                case (byte)MSG_TYPE.DSC_MSG_TYPE_GRP_CALL:
                    AddReceiveGrpCallMsgDGV();
                    break;
                case (byte)MSG_TYPE.DSC_MSG_TYPE_GEO_AREA:
                    AddReceiveGeoAreaMsgDGV();
                    break;
                case (byte)MSG_TYPE.DSC_MSG_TYPE_INDI_CALL:
                    AddReceiveIndiCallMsgDGV();
                    break;
                case (byte)MSG_TYPE.DSC_MSG_TYPE_SEMI_AUTO:
                    AddReceiveSemiAUTOMsgDGV();
                    break;
            }
        }

        private void btn_rx_clear_Click(object sender, EventArgs e)
        {
            lb_rx_dsc.Text = "RX DSC (0)";
            dgvDscRsv.Rows.Clear();
        }

        private void btn_import_Click(object sender, EventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.InitialDirectory = @"D:\";
            dlg.Title = "Open CSV Files";
            dlg.CheckFileExists = true;
            dlg.CheckPathExists = true;
            dlg.DefaultExt = "CSV";
            dlg.Filter = "CSV files (*.csv)|*.csv|All Files (*.*)|*.*";
            dlg.FilterIndex = 1;
            dlg.RestoreDirectory = true;
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                string rowValue;
                string[] cellValue;
                dgvDscRsv.Rows.Clear();

                if (System.IO.File.Exists(dlg.FileName))
                {
                    System.IO.StreamReader sr = new System.IO.StreamReader(dlg.FileName);

                    //Reading header
                    rowValue = sr.ReadLine();
                    cellValue = rowValue.Split(',');

                    while (sr.Peek() != -1)
                    {
                        rowValue = sr.ReadLine();
                        cellValue = rowValue.Split(',');
                        int rowIndex = dgvDscRsv.Rows.Add(cellValue);
                        rowIndex++;
                        lb_rx_dsc.Text = "RX DSC (" + rowIndex.ToString() + ")";
                    }
                    sr.Close();
                }
                else
                {
                    MessageBox.Show("No File is Selected");
                }
            }
        }

        private void btn_export_Click(object sender, EventArgs e)
        {
            using (SaveFileDialog dlg = new SaveFileDialog())
            {
                dlg.Filter = "csv (*.csv) | *.csv";
                if (dlg.ShowDialog() == DialogResult.OK)
                {
                    Utill.Save_Csv(dlg.FileName, dgvDscRsv, true);
                }
            }
        }

        private void btnDSCAdd_Click(object sender, EventArgs e)
        {

            m_DscForm = new DscComposeForm(true);

            m_DscForm.SetDefaultValue();

            m_DscForm.DataReceived += DscForm_DataReceived;

            m_DscForm.Show();
        }

        private void DscForm_DataReceived(object sender, DscCompose data)
        {
            int rowIndex = -1;

            if (data.msg_type.Equals(TYPE_DIST))
            {
                if (data.m_IsAdd == false)
                {
                    rowIndex = m_dsc_edit_row_idx;

                    dgvDscSend.Rows[rowIndex].Cells[0].Value = data.msg_type;
                    dgvDscSend.Rows[rowIndex].Cells[1].Value = data.msg_format;
                    dgvDscSend.Rows[rowIndex].Cells[2].Value = "N/A";
                    dgvDscSend.Rows[rowIndex].Cells[3].Value = "N/A";
                    dgvDscSend.Rows[rowIndex].Cells[4].Value = data.mmsi_call_from;
                    dgvDscSend.Rows[rowIndex].Cells[5].Value = "N/A";
                    dgvDscSend.Rows[rowIndex].Cells[6].Value = "N/A";
                    dgvDscSend.Rows[rowIndex].Cells[7].Value = "N/A";
                    dgvDscSend.Rows[rowIndex].Cells[8].Value = data.sub_com;
                    dgvDscSend.Rows[rowIndex].Cells[9].Value = "N/A";
                    dgvDscSend.Rows[rowIndex].Cells[10].Value = "N/A";
                    dgvDscSend.Rows[rowIndex].Cells[11].Value = data.nature;
                    dgvDscSend.Rows[rowIndex].Cells[12].Value = data.latitude;
                    dgvDscSend.Rows[rowIndex].Cells[13].Value = data.longitude;
                    dgvDscSend.Rows[rowIndex].Cells[14].Value = data.utc;
                    dgvDscSend.Rows[rowIndex].Cells[15].Value = data.eos;
                    dgvDscSend.Rows[rowIndex].Cells[16].Value = data.expn_mode;
                }
                else
                {
                    rowIndex = dgvDscSend.Rows.Add(data.msg_type, data.msg_format, "N/A", "N/A", data.mmsi_call_from, "N/A", "N/A", "N/A",
                                                   data.sub_com, "N/A", "N/A", data.nature, data.latitude, data.longitude, data.utc, data.eos, data.expn_mode, "->");

                    int row_idx = rowIndex;

                    row_idx++;

                    lb_tx_dsc.Text = "TX DSC (" + row_idx.ToString() + ")";
                }
            }
            else if (data.msg_type.Equals(TYPE_DIST_ACK))
            {
                if (data.m_IsAdd == false)
                {
                    rowIndex = m_dsc_edit_row_idx;

                    dgvDscSend.Rows[rowIndex].Cells[0].Value = data.msg_type;       // message type
                    dgvDscSend.Rows[rowIndex].Cells[1].Value = data.msg_format;     // message format
                    dgvDscSend.Rows[rowIndex].Cells[2].Value = "N/A";               // addr
                    dgvDscSend.Rows[rowIndex].Cells[3].Value = data.msg_category;   // category
                    dgvDscSend.Rows[rowIndex].Cells[4].Value = data.mmsi_call_from; // self id 
                    dgvDscSend.Rows[rowIndex].Cells[5].Value = data.teleCom;        // telecmd
                    dgvDscSend.Rows[rowIndex].Cells[6].Value = "N/A";               // telcmd1
                    dgvDscSend.Rows[rowIndex].Cells[7].Value = "N/A";               // telcmd2
                    dgvDscSend.Rows[rowIndex].Cells[8].Value = data.sub_com;        // subcmd
                    dgvDscSend.Rows[rowIndex].Cells[9].Value = "N/A";               // freq
                    dgvDscSend.Rows[rowIndex].Cells[10].Value = data.mmsi_distress; // distress id
                    dgvDscSend.Rows[rowIndex].Cells[11].Value = data.nature;        // natrue
                    dgvDscSend.Rows[rowIndex].Cells[12].Value = data.latitude;      // latitude
                    dgvDscSend.Rows[rowIndex].Cells[13].Value = data.longitude;     // longitude
                    dgvDscSend.Rows[rowIndex].Cells[14].Value = data.utc;           // utc
                    dgvDscSend.Rows[rowIndex].Cells[15].Value = data.eos;           // eos
                    dgvDscSend.Rows[rowIndex].Cells[16].Value = data.expn_mode;     // expan mode
                }
                else
                {
                    rowIndex = dgvDscSend.Rows.Add(data.msg_type, data.msg_format, "N/A", data.msg_category,
                                                   data.mmsi_call_from, data.teleCom, "N/A", "N/A", data.sub_com, "N/A", data.mmsi_distress,
                                                   data.nature, data.latitude, data.longitude, data.utc, data.eos, data.expn_mode, "->");

                    int row_idx = rowIndex;

                    row_idx++;

                    lb_tx_dsc.Text = "TX DSC (" + row_idx.ToString() + ")";
                }
            }
            else if (data.msg_type.Equals(TYPE_DIST_RLY) || data.msg_type.Equals(TYPE_DIST_RLY_ACK))
            {
                if (data.m_IsAdd == false)
                {
                    rowIndex = m_dsc_edit_row_idx;

                    dgvDscSend.Rows[rowIndex].Cells[0].Value = data.msg_type;       // message type
                    dgvDscSend.Rows[rowIndex].Cells[1].Value = data.msg_format;     // message format

                    if (data.msg_format.Equals(FMT_116))
                    {
                        dgvDscSend.Rows[rowIndex].Cells[2].Value = "N/A";        /// addr
                    }
                    else if (data.msg_format.Equals(FMT_102))
                    {
                        dgvDscSend.Rows[rowIndex].Cells[2].Value = data.area;      /// addr
                    }
                    else
                    {
                        dgvDscSend.Rows[rowIndex].Cells[2].Value = data.mmsi_call_to;   // addr
                    }

                    dgvDscSend.Rows[rowIndex].Cells[3].Value = data.msg_category;   // category
                    dgvDscSend.Rows[rowIndex].Cells[4].Value = data.mmsi_call_from; // self id 
                    dgvDscSend.Rows[rowIndex].Cells[5].Value = data.teleCom;        // telecmd
                    dgvDscSend.Rows[rowIndex].Cells[6].Value = "N/A";               // telcmd1
                    dgvDscSend.Rows[rowIndex].Cells[7].Value = "N/A";               // telcmd2
                    dgvDscSend.Rows[rowIndex].Cells[8].Value = data.sub_com;        // subcmd
                    dgvDscSend.Rows[rowIndex].Cells[9].Value = "N/A";               // freq
                    dgvDscSend.Rows[rowIndex].Cells[10].Value = data.mmsi_distress; // distress id
                    dgvDscSend.Rows[rowIndex].Cells[11].Value = data.nature;        // natrue
                    dgvDscSend.Rows[rowIndex].Cells[12].Value = data.latitude;      // latitude
                    dgvDscSend.Rows[rowIndex].Cells[13].Value = data.longitude;     // longitude
                    dgvDscSend.Rows[rowIndex].Cells[14].Value = data.utc;           // utc
                    dgvDscSend.Rows[rowIndex].Cells[15].Value = data.eos;           // eos
                    dgvDscSend.Rows[rowIndex].Cells[16].Value = data.expn_mode;     // expan mode
                }
                else
                {
                    if (data.msg_format.Equals(FMT_116))
                    {
                        rowIndex = dgvDscSend.Rows.Add(data.msg_type, data.msg_format, "N/A", data.msg_category,
                                                   data.mmsi_call_from, data.teleCom, "N/A", "N/A", data.sub_com, "N/A", data.mmsi_distress,
                                                   data.nature, data.latitude, data.longitude, data.utc, data.eos, data.expn_mode, "->");

                    }
                    else if (data.msg_format.Equals(FMT_102))
                    {
                        rowIndex = dgvDscSend.Rows.Add(data.msg_type, data.msg_format, data.area, data.msg_category,
                                                   data.mmsi_call_from, data.teleCom, "N/A", "N/A", data.sub_com, "N/A", data.mmsi_distress,
                                                   data.nature, data.latitude, data.longitude, data.utc, data.eos, data.expn_mode, "->");
                    }
                    else
                    {
                        rowIndex = dgvDscSend.Rows.Add(data.msg_type, data.msg_format, data.mmsi_call_to, data.msg_category,
                                                       data.mmsi_call_from, data.teleCom, "N/A", "N/A", data.sub_com, "N/A", data.mmsi_distress,
                                                       data.nature, data.latitude, data.longitude, data.utc, data.eos, data.expn_mode, "->");
                    }

                    int row_idx = rowIndex;

                    row_idx++;

                    lb_tx_dsc.Text = "TX DSC (" + row_idx.ToString() + ")";
                }
            }
            else if (data.msg_type.Equals(TYPE_ALLSHIPS))
            {
                if (data.m_IsAdd == false)
                {
                    rowIndex = m_dsc_edit_row_idx;

                    dgvDscSend.Rows[rowIndex].Cells[0].Value = data.msg_type;       // message type
                    dgvDscSend.Rows[rowIndex].Cells[1].Value = data.msg_format;     // message format
                    dgvDscSend.Rows[rowIndex].Cells[2].Value = "N/A";               // addr
                    dgvDscSend.Rows[rowIndex].Cells[3].Value = data.msg_category;   // category
                    dgvDscSend.Rows[rowIndex].Cells[4].Value = data.mmsi_call_from; // self id 
                    dgvDscSend.Rows[rowIndex].Cells[5].Value = "N/A";               // telecmd
                    dgvDscSend.Rows[rowIndex].Cells[6].Value = data.teleCom1;       // telcmd1
                    dgvDscSend.Rows[rowIndex].Cells[7].Value = data.teleCom2;       // telcmd2
                    dgvDscSend.Rows[rowIndex].Cells[8].Value = "N/A";               // subcmd
                    dgvDscSend.Rows[rowIndex].Cells[9].Value = data.freq;           // freq
                    dgvDscSend.Rows[rowIndex].Cells[10].Value = "N/A";              // distress id
                    dgvDscSend.Rows[rowIndex].Cells[11].Value = "N/A";              // nature
                    dgvDscSend.Rows[rowIndex].Cells[12].Value = "N/A";              // latitude
                    dgvDscSend.Rows[rowIndex].Cells[13].Value = "N/A";              // longitude
                    dgvDscSend.Rows[rowIndex].Cells[14].Value = "N/A";              // utc
                    dgvDscSend.Rows[rowIndex].Cells[15].Value = data.eos;           // eos
                    dgvDscSend.Rows[rowIndex].Cells[16].Value = data.expn_mode;     // expan mode
                }
                else
                {
                    rowIndex = dgvDscSend.Rows.Add(data.msg_type, data.msg_format, "N/A", data.msg_category,
                                                   data.mmsi_call_from, "N/A", data.teleCom1, data.teleCom2, "N/A", data.freq, "N/A",
                                                   "N/A", "N/A", "N/A", "N/A", data.eos, data.expn_mode, "->");

                    int row_idx = rowIndex;

                    row_idx++;

                    lb_tx_dsc.Text = "TX DSC (" + row_idx.ToString() + ")";
                }
            }
            else if (data.msg_type.Equals(TYPE_INDIVIDUAL))
            {
                if (data.m_IsAdd == false)
                {
                    rowIndex = m_dsc_edit_row_idx;

                    dgvDscSend.Rows[rowIndex].Cells[0].Value = data.msg_type;       // message type
                    dgvDscSend.Rows[rowIndex].Cells[1].Value = data.msg_format;     // message format
                    dgvDscSend.Rows[rowIndex].Cells[2].Value = data.mmsi_call_to;   // addr
                    dgvDscSend.Rows[rowIndex].Cells[3].Value = data.msg_category;   // category
                    dgvDscSend.Rows[rowIndex].Cells[4].Value = data.mmsi_call_from; // self id 
                    dgvDscSend.Rows[rowIndex].Cells[5].Value = "N/A";               // telecmd
                    dgvDscSend.Rows[rowIndex].Cells[6].Value = data.teleCom1;       // telcmd1
                    dgvDscSend.Rows[rowIndex].Cells[7].Value = data.teleCom2;       // telcmd2
                    dgvDscSend.Rows[rowIndex].Cells[8].Value = "N/A";               // subcmd
                    dgvDscSend.Rows[rowIndex].Cells[9].Value = data.freq;           // freq
                    dgvDscSend.Rows[rowIndex].Cells[10].Value = "N/A";              // distress id
                    dgvDscSend.Rows[rowIndex].Cells[11].Value = "N/A";              // nature
                    dgvDscSend.Rows[rowIndex].Cells[12].Value = data.latitude;          // latitude
                    dgvDscSend.Rows[rowIndex].Cells[13].Value = data.longitude;         // longitude
                    if (data.teleCom1.Equals(TELCOM1_121) && data.eos.Equals(EOS_122))
                        dgvDscSend.Rows[rowIndex].Cells[14].Value = data.utc;              // utc
                    else
                        dgvDscSend.Rows[rowIndex].Cells[14].Value = "N/A";              // utc


                    dgvDscSend.Rows[rowIndex].Cells[15].Value = data.eos;           // eos
                    dgvDscSend.Rows[rowIndex].Cells[16].Value = data.expn_mode;     // expan mode
                }
                else
                {
                    if (data.teleCom1.Equals(TELCOM1_121) && data.eos.Equals(EOS_122))
                    {
                        rowIndex = dgvDscSend.Rows.Add(data.msg_type, data.msg_format, data.mmsi_call_to, data.msg_category,
                                                   data.mmsi_call_from, "N/A", data.teleCom1, data.teleCom2, "N/A", data.freq, "N/A",
                                                   "N/A", data.latitude, data.longitude, data.utc, data.eos, data.expn_mode, "->");
                    }
                    else
                    {
                        rowIndex = dgvDscSend.Rows.Add(data.msg_type, data.msg_format, data.mmsi_call_to, data.msg_category,
                                                   data.mmsi_call_from, "N/A", data.teleCom1, data.teleCom2, "N/A", data.freq, "N/A",
                                                   "N/A", data.latitude, data.longitude, "N/A", data.eos, data.expn_mode, "->");
                    }

                    int row_idx = rowIndex;

                    row_idx++;

                    lb_tx_dsc.Text = "TX DSC (" + row_idx.ToString() + ")";
                }
            }
            else if (data.msg_type.Equals(TYPE_GROUP_CALL))
            {
                if (data.m_IsAdd == false)
                {
                    rowIndex = m_dsc_edit_row_idx;

                    dgvDscSend.Rows[rowIndex].Cells[0].Value = data.msg_type;       // message type
                    dgvDscSend.Rows[rowIndex].Cells[1].Value = data.msg_format;     // message format
                    dgvDscSend.Rows[rowIndex].Cells[2].Value = data.mmsi_call_to;   // addr
                    dgvDscSend.Rows[rowIndex].Cells[3].Value = data.msg_category;   // category
                    dgvDscSend.Rows[rowIndex].Cells[4].Value = data.mmsi_call_from; // self id 
                    dgvDscSend.Rows[rowIndex].Cells[5].Value = "N/A";               // telecmd
                    dgvDscSend.Rows[rowIndex].Cells[6].Value = data.teleCom1;       // telcmd1
                    dgvDscSend.Rows[rowIndex].Cells[7].Value = data.teleCom2;       // telcmd2
                    dgvDscSend.Rows[rowIndex].Cells[8].Value = "N/A";               // subcmd
                    dgvDscSend.Rows[rowIndex].Cells[9].Value = data.freq;           // freq
                    dgvDscSend.Rows[rowIndex].Cells[10].Value = "N/A";              // distress id
                    dgvDscSend.Rows[rowIndex].Cells[11].Value = "N/A";              // nature
                    dgvDscSend.Rows[rowIndex].Cells[12].Value = "N/A";              // latitude
                    dgvDscSend.Rows[rowIndex].Cells[13].Value = "N/A";              // longitude
                    dgvDscSend.Rows[rowIndex].Cells[14].Value = "N/A";              // utc
                    dgvDscSend.Rows[rowIndex].Cells[15].Value = data.eos;           // eos
                    dgvDscSend.Rows[rowIndex].Cells[16].Value = data.expn_mode;     // expan mode
                }
                else
                {
                    rowIndex = dgvDscSend.Rows.Add(data.msg_type, data.msg_format, data.mmsi_call_to, data.msg_category,
                                                   data.mmsi_call_from, "N/A", data.teleCom1, data.teleCom2, "N/A", data.freq, "N/A",
                                                   "N/A", "N/A", "N/A", "N/A", data.eos, data.expn_mode, "->");

                    int row_idx = rowIndex;

                    row_idx++;

                    lb_tx_dsc.Text = "TX DSC (" + row_idx.ToString() + ")";
                }
            }
            else if (data.msg_type.Equals(TYPE_SEMI_AUTO))
            {
                if (data.m_IsAdd == false)
                {
                    rowIndex = m_dsc_edit_row_idx;

                    dgvDscSend.Rows[rowIndex].Cells[0].Value = data.msg_type;       // message type
                    dgvDscSend.Rows[rowIndex].Cells[1].Value = data.msg_format;     // message format
                    dgvDscSend.Rows[rowIndex].Cells[2].Value = data.mmsi_call_to;   // addr
                    dgvDscSend.Rows[rowIndex].Cells[3].Value = data.msg_category;   // category
                    dgvDscSend.Rows[rowIndex].Cells[4].Value = data.mmsi_call_from; // self id 
                    dgvDscSend.Rows[rowIndex].Cells[5].Value = "N/A";               // telecmd
                    dgvDscSend.Rows[rowIndex].Cells[6].Value = data.teleCom1;       // telcmd1
                    dgvDscSend.Rows[rowIndex].Cells[7].Value = data.teleCom2;       // telcmd2
                    dgvDscSend.Rows[rowIndex].Cells[8].Value = "N/A";               // subcmd
                    dgvDscSend.Rows[rowIndex].Cells[9].Value = data.freq;           // freq
                    dgvDscSend.Rows[rowIndex].Cells[10].Value = data.tel_num;       // distress id or tel number
                    dgvDscSend.Rows[rowIndex].Cells[11].Value = "N/A";              // nature
                    dgvDscSend.Rows[rowIndex].Cells[12].Value = "N/A";              // latitude
                    dgvDscSend.Rows[rowIndex].Cells[13].Value = "N/A";              // longitude
                    dgvDscSend.Rows[rowIndex].Cells[14].Value = "N/A";              // utc
                    dgvDscSend.Rows[rowIndex].Cells[15].Value = data.eos;           // eos
                    dgvDscSend.Rows[rowIndex].Cells[16].Value = data.expn_mode;     // expan mode
                }
                else
                {
                    rowIndex = dgvDscSend.Rows.Add(data.msg_type, data.msg_format, data.mmsi_call_to, data.msg_category,
                                                   data.mmsi_call_from, "N/A", data.teleCom1, data.teleCom2, "N/A", data.freq, data.tel_num,
                                                   "N/A", "N/A", "N/A", "N/A", data.eos, data.expn_mode, "->");

                    int row_idx = rowIndex;
                    row_idx++;
                    lb_tx_dsc.Text = "TX DSC (" + row_idx.ToString() + ")";
                }
            }


            for (int col = 0; col < dgvDscSend.Columns.Count; col++)
            {
                var cellValue = dgvDscSend[col, rowIndex].Value;

                if (cellValue != null && cellValue.ToString().Equals("N/A"))
                {
                    dgvDscSend.Rows[rowIndex].Cells[col].Style.BackColor = Color.LightGray;
                }
            }

        }

        private void btnDSCDelete_Click(object sender, EventArgs e)
        {
            foreach (DataGridViewCell oneCell in dgvDscSend.SelectedCells)
            {
                if (oneCell.Selected)
                    dgvDscSend.Rows.RemoveAt(oneCell.RowIndex);
            }
        }

        private void btnDscEdit_Click(object sender, EventArgs e)
        {
            DscComposeForm DscForm = new DscComposeForm(false);

            DscCompose dsc_msg = new DscCompose();

            foreach (DataGridViewCell oneCell in dgvDscSend.SelectedCells)
            {
                if (oneCell.Selected)
                {
                    m_dsc_edit_row_idx = oneCell.RowIndex;
                    dsc_msg.msg_type = dgvDscSend.Rows[oneCell.RowIndex].Cells[0].Value.ToString();
                    dsc_msg.msg_format = dgvDscSend.Rows[oneCell.RowIndex].Cells[1].Value.ToString();

                    if (dsc_msg.msg_format.Equals(FMT_102))
                        dsc_msg.area = dgvDscSend.Rows[oneCell.RowIndex].Cells[2].Value.ToString();
                    else
                        dsc_msg.mmsi_call_to = dgvDscSend.Rows[oneCell.RowIndex].Cells[2].Value.ToString();

                    dsc_msg.msg_category = dgvDscSend.Rows[oneCell.RowIndex].Cells[3].Value.ToString();
                    dsc_msg.mmsi_call_from = dgvDscSend.Rows[oneCell.RowIndex].Cells[4].Value.ToString();
                    dsc_msg.teleCom = dgvDscSend.Rows[oneCell.RowIndex].Cells[5].Value.ToString();
                    dsc_msg.teleCom1 = dgvDscSend.Rows[oneCell.RowIndex].Cells[6].Value.ToString();
                    dsc_msg.teleCom2 = dgvDscSend.Rows[oneCell.RowIndex].Cells[7].Value.ToString();
                    dsc_msg.sub_com = dgvDscSend.Rows[oneCell.RowIndex].Cells[8].Value.ToString();
                    dsc_msg.freq = dgvDscSend.Rows[oneCell.RowIndex].Cells[9].Value.ToString();
                    dsc_msg.mmsi_distress = dgvDscSend.Rows[oneCell.RowIndex].Cells[10].Value.ToString();
                    dsc_msg.nature = dgvDscSend.Rows[oneCell.RowIndex].Cells[11].Value.ToString();
                    dsc_msg.latitude = dgvDscSend.Rows[oneCell.RowIndex].Cells[12].Value.ToString();
                    dsc_msg.longitude = dgvDscSend.Rows[oneCell.RowIndex].Cells[13].Value.ToString();
                    dsc_msg.utc = dgvDscSend.Rows[oneCell.RowIndex].Cells[14].Value.ToString();
                    dsc_msg.eos = dgvDscSend.Rows[oneCell.RowIndex].Cells[15].Value.ToString();
                    dsc_msg.expn_mode = dgvDscSend.Rows[oneCell.RowIndex].Cells[16].Value.ToString();
                }
            }

            DscForm.SetDscMsgValue(dsc_msg);
            DscForm.DataReceived += DscForm_DataReceived;
            DscForm.Show();
        }

        private void SelectionChanged_DscTx(object sender, EventArgs e)
        {
            if (dgvDscSend.SelectedCells.Count > 0)
            {
                btnDscEdit.Visible = true;
            }
            else
            {
                btnDscEdit.Visible = false;
            }
        }

        private void btn_tx_import_Click(object sender, EventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.InitialDirectory = @"D:\";
            dlg.Title = "Open CSV Files";
            dlg.CheckFileExists = true;
            dlg.CheckPathExists = true;
            dlg.DefaultExt = "CSV";
            dlg.Filter = "CSV files (*.csv)|*.csv|All Files (*.*)|*.*";
            dlg.FilterIndex = 1;
            dlg.RestoreDirectory = true;
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                string rowValue;
                string[] cellValue;
                dgvDscRsv.Rows.Clear();

                if (System.IO.File.Exists(dlg.FileName))
                {
                    System.IO.StreamReader sr = new System.IO.StreamReader(dlg.FileName);

                    //Reading header
                    rowValue = sr.ReadLine();
                    cellValue = rowValue.Split(',');

                    while (sr.Peek() != -1)
                    {
                        rowValue = sr.ReadLine();
                        cellValue = rowValue.Split(',');
                        int rowIndex = dgvDscSend.Rows.Add(cellValue);
                        rowIndex++;
                        lb_tx_dsc.Text = "TX DSC (" + rowIndex.ToString() + ")";
                    }
                    sr.Close();
                }
                else
                {
                    MessageBox.Show("No File is Selected");
                }
            }
        }

        private void btn_tx_export_Click(object sender, EventArgs e)
        {
            using (SaveFileDialog dlg = new SaveFileDialog())
            {
                dlg.Filter = "csv (*.csv) | *.csv";
                if (dlg.ShowDialog() == DialogResult.OK)
                {
                    Utill.Save_Csv(dlg.FileName, dgvDscSend, true);
                }
            }
        }

        private void btnDSCClear_Click(object sender, EventArgs e)
        {
            lb_rx_dsc.Text = "RX DSC (0)";
            dgvDscSend.Rows.Clear();
        }


        private void SendDistressAlert(DscCompose msg)
        {
            ushort cmd;
            byte cmd_type = DataProtocol.CMD_REQ;
            ushort cmd_param = DataProtocol.CMD_REQ_TX_DSC;
            cmd = m_DataProtocol.GenerateCMD(cmd_type, m_Sender, m_Receiver);

            m_DataProtocol.m_DstAlert.Head.msg_type = MSG_TYPE.DSC_MSG_TYPE_DST_ALT;

            // Format
            m_DataProtocol.m_DstAlert.Format = m_DscMessage.ConvertFormatToNum(msg.msg_format);

            // Self Id
            if (msg.mmsi_call_from.Length == 9)
            {
                msg.mmsi_call_from += "0";
            }
            else
            {
                MessageBox.Show("Self ID must be 9 digits.");
                return;
            }

            string temp;
            for (int i = 0; i < 5; i++)
            {
                temp = msg.mmsi_call_from.Substring(i * 2, 2);
                m_DataProtocol.m_DstAlert.SelfID[i] = Convert.ToByte(temp);
            }

            // Nature
            m_DataProtocol.m_DstAlert.Nature = m_DscMessage.ConvertNatureToNum(msg.nature);

            string position = null;
            string latitudeMinutesExpn = null;
            string longitudeMinutesExpn = null;

            DscComposeForm.EncodePos1(msg.latitude, msg.longitude, ref position, ref latitudeMinutesExpn, ref longitudeMinutesExpn);

            for (int i = 0; i < 5; i++)
            {
                temp = position.Substring(i * 2, 2);
                m_DataProtocol.m_DstAlert.DstPos[i] = Convert.ToByte(temp);
            }

            string utc = null;
            DscComposeForm.EncodeUTC(msg.utc, ref utc);

            for (int i = 0; i < 2; i++)
            {
                temp = utc.Substring(i * 2, 2);
                m_DataProtocol.m_DstAlert.UTC[i] = Convert.ToByte(temp);
            }

            // Sub Com
            m_DataProtocol.m_DstAlert.SubCom = m_DscMessage.ConvertTelCom1StringToNum(msg.sub_com);

            // EOS
            m_DataProtocol.m_DstAlert.EOS = m_DscMessage.ConvertEosStringToNum(msg.eos);

            // expand 
            //100,X1,X2,Y1,Y2,EOS
            if (longitudeMinutesExpn != null)
            {
                m_DataProtocol.m_DstAlert.ExpPos[0] = 100;

                for (int i = 0; i < 2; i++) // latitude
                {
                    temp = latitudeMinutesExpn.Substring(i * 2, 2);
                    m_DataProtocol.m_DstAlert.ExpPos[i + 1] = Convert.ToByte(temp);
                }

                for (int i = 0; i < 2; i++) // longitude
                {
                    temp = longitudeMinutesExpn.Substring(i * 2, 2);
                    m_DataProtocol.m_DstAlert.ExpPos[i + 3] = Convert.ToByte(temp);
                }

                m_DataProtocol.m_DstAlert.ExpPos[5] = m_DscMessage.ConvertExpnStringToNum(msg.expn_mode); // Expand Mode
            }

            m_DataProtocol.EncodeDistressAlert(ref m_DataProtocol.m_DataFrame.data_buf);

            if (m_DataProtocol.CheckStatus() == PROTO_STAT.IDLE_STAT)
            {
                m_DataProtocol.SendDataFrame(cmd, cmd_param, m_DataProtocol.m_DataFrame.data_buf, DSC_MSG_SIZE);
                m_DataProtocol.SetProtocoStatus(PROTO_STAT.WAIT_FOR_ACK);
            }
        }

        private void SendDistressAlertAck(DscCompose msg)
        {
            ushort cmd;
            byte cmd_type = DataProtocol.CMD_REQ;
            ushort cmd_param = DataProtocol.CMD_REQ_TX_DSC;
            cmd = m_DataProtocol.GenerateCMD(cmd_type, m_Sender, m_Receiver);

            m_DataProtocol.m_DstAlertAck.Head.msg_type = MSG_TYPE.DSC_MSG_TYPE_DST_ACK;

            // Format
            m_DataProtocol.m_DstAlertAck.Format = m_DscMessage.ConvertFormatToNum(msg.msg_format);

            // Category
            m_DataProtocol.m_DstAlertAck.Category = m_DscMessage.ConvertCategoryStringToNum(msg.msg_category);

            // Self Id
            if (msg.mmsi_call_from.Length == 9)
            {
                msg.mmsi_call_from += "0";
            }
            else
            {
                MessageBox.Show("Self ID must be 9 digits.");
                return;
            }

            string temp;
            for (int i = 0; i < 5; i++)
            {
                temp = msg.mmsi_call_from.Substring(i * 2, 2);
                m_DataProtocol.m_DstAlertAck.SelfID[i] = Convert.ToByte(temp);
            }

            // telcom0
            m_DataProtocol.m_DstAlertAck.TelCmd0 = m_DscMessage.ConvertTelCom1StringToNum(msg.teleCom);


            if (msg.mmsi_distress.Length == 9)
            {
                msg.mmsi_distress += "0";
            }
            else
            {
                MessageBox.Show("Distress ID must be 9 digits.");
                return;
            }

            temp = null;
            for (int i = 0; i < 5; i++)
            {
                temp = msg.mmsi_distress.Substring(i * 2, 2);
                m_DataProtocol.m_DstAlertAck.DistID[i] = Convert.ToByte(temp);
            }

            // Nature
            m_DataProtocol.m_DstAlertAck.Nature = m_DscMessage.ConvertNatureToNum(msg.nature);

            string position = null;
            string latitudeMinutesExpn = null;
            string longitudeMinutesExpn = null;

            DscComposeForm.EncodePos1(msg.latitude, msg.longitude, ref position, ref latitudeMinutesExpn, ref longitudeMinutesExpn);

            for (int i = 0; i < 5; i++)
            {
                temp = position.Substring(i * 2, 2);
                m_DataProtocol.m_DstAlertAck.DstPos[i] = Convert.ToByte(temp);
            }

            string utc = null;
            DscComposeForm.EncodeUTC(msg.utc, ref utc);

            for (int i = 0; i < 2; i++)
            {
                temp = utc.Substring(i * 2, 2);
                m_DataProtocol.m_DstAlertAck.UTC[i] = Convert.ToByte(temp);
            }

            // Sub Com
            m_DataProtocol.m_DstAlertAck.SubCom = m_DscMessage.ConvertTelCom1StringToNum(msg.sub_com);

            // EOS
            m_DataProtocol.m_DstAlertAck.EOS = m_DscMessage.ConvertEosStringToNum(msg.eos);

            // expand 
            //100,X1,X2,Y1,Y2,EOS
            if (longitudeMinutesExpn != null)
            {
                m_DataProtocol.m_DstAlertAck.ExpPos[0] = 100;

                for (int i = 0; i < 2; i++) // latitude
                {
                    temp = latitudeMinutesExpn.Substring(i * 2, 2);
                    m_DataProtocol.m_DstAlertAck.ExpPos[i + 1] = Convert.ToByte(temp);
                }

                for (int i = 0; i < 2; i++) // longitude
                {
                    temp = longitudeMinutesExpn.Substring(i * 2, 2);
                    m_DataProtocol.m_DstAlertAck.ExpPos[i + 3] = Convert.ToByte(temp);
                }

                m_DataProtocol.m_DstAlert.ExpPos[5] = m_DscMessage.ConvertExpnStringToNum(msg.expn_mode); // Expand Mode
            }

            m_DataProtocol.EncodeDistressAlertAck(ref m_DataProtocol.m_DataFrame.data_buf);

            if (m_DataProtocol.CheckStatus() == PROTO_STAT.IDLE_STAT)
            {
                m_DataProtocol.SendDataFrame(cmd, cmd_param, m_DataProtocol.m_DataFrame.data_buf, DSC_MSG_SIZE);
                m_DataProtocol.SetProtocoStatus(PROTO_STAT.WAIT_FOR_ACK);
            }
        }

        private void SendDistressAlertRelay(DscCompose msg)
        {
            ushort cmd;
            byte cmd_type = DataProtocol.CMD_REQ;
            ushort cmd_param = DataProtocol.CMD_REQ_TX_DSC;
            cmd = m_DataProtocol.GenerateCMD(cmd_type, m_Sender, m_Receiver);

            m_DataProtocol.m_DstAlertRelay.Head.msg_type = MSG_TYPE.DSC_MSG_TYPE_DST_RLY;

            // Format
            m_DataProtocol.m_DstAlertRelay.Format = m_DscMessage.ConvertFormatToNum(msg.msg_format);

            // Addr

            string temp;
            if (msg.msg_format.Equals(FMT_102))
            {
                string area = DscComposeForm.EncodeArea(ref msg.area);

                for (int i = 0; i < 5; i++)
                {
                    temp = area.Substring(i * 2, 2);
                    m_DataProtocol.m_DstAlertRelay.AddrID[i] = Convert.ToByte(temp);
                }
            }
            else
            {
                if (msg.mmsi_call_to.Equals("N/A"))
                {
                    msg.mmsi_call_to = "0000000000";
                }
                else
                {
                    if (msg.mmsi_call_to.Length == 9)
                    {
                        msg.mmsi_call_to += "0";
                    }
                    else
                    {
                        MessageBox.Show("Addr ID must be 9 digits.");
                        return;
                    }
                }

                for (int i = 0; i < 5; i++)
                {
                    temp = msg.mmsi_call_to.Substring(i * 2, 2);
                    m_DataProtocol.m_DstAlertRelay.AddrID[i] = Convert.ToByte(temp);
                }
            }

            // Category
            m_DataProtocol.m_DstAlertRelay.Category = m_DscMessage.ConvertCategoryStringToNum(msg.msg_category);

            // Self Id
            if (msg.mmsi_call_from.Length == 9)
            {
                msg.mmsi_call_from += "0";
            }
            else
            {
                MessageBox.Show("Self ID must be 9 digits.");
                return;
            }

            temp = null;
            for (int i = 0; i < 5; i++)
            {
                temp = msg.mmsi_call_from.Substring(i * 2, 2);
                m_DataProtocol.m_DstAlertRelay.SelfID[i] = Convert.ToByte(temp);
            }

            // telcom0
            m_DataProtocol.m_DstAlertRelay.TelCmd0 = m_DscMessage.ConvertTelCom1StringToNum(msg.teleCom);

            // distress id
            if (msg.mmsi_distress.Length == 9)
            {
                msg.mmsi_distress += "0";
            }
            else
            {
                MessageBox.Show("Distress ID must be 9 digits.");
                return;
            }

            temp = null;
            for (int i = 0; i < 5; i++)
            {
                temp = msg.mmsi_distress.Substring(i * 2, 2);
                m_DataProtocol.m_DstAlertRelay.DistID[i] = Convert.ToByte(temp);
            }

            // Nature
            m_DataProtocol.m_DstAlertRelay.Nature = m_DscMessage.ConvertNatureToNum(msg.nature);

            string position = null;
            string latitudeMinutesExpn = null;
            string longitudeMinutesExpn = null;

            DscComposeForm.EncodePos1(msg.latitude, msg.longitude, ref position, ref latitudeMinutesExpn, ref longitudeMinutesExpn);

            for (int i = 0; i < 5; i++)
            {
                temp = position.Substring(i * 2, 2);
                m_DataProtocol.m_DstAlertRelay.DstPos[i] = Convert.ToByte(temp);
            }

            string utc = null;
            DscComposeForm.EncodeUTC(msg.utc, ref utc);

            for (int i = 0; i < 2; i++)
            {
                temp = utc.Substring(i * 2, 2);
                m_DataProtocol.m_DstAlertRelay.UTC[i] = Convert.ToByte(temp);
            }

            // Sub Com
            m_DataProtocol.m_DstAlertRelay.SubCom = m_DscMessage.ConvertTelCom1StringToNum(msg.sub_com);

            // EOS
            m_DataProtocol.m_DstAlertRelay.EOS = m_DscMessage.ConvertEosStringToNum(msg.eos);

            // expand 
            //100,X1,X2,Y1,Y2,EOS
            if (longitudeMinutesExpn != null)
            {
                m_DataProtocol.m_DstAlertRelay.ExpPos[0] = 100;

                for (int i = 0; i < 2; i++) // latitude
                {
                    temp = latitudeMinutesExpn.Substring(i * 2, 2);
                    m_DataProtocol.m_DstAlertRelay.ExpPos[i + 1] = Convert.ToByte(temp);
                }

                for (int i = 0; i < 2; i++) // longitude
                {
                    temp = longitudeMinutesExpn.Substring(i * 2, 2);
                    m_DataProtocol.m_DstAlertRelay.ExpPos[i + 3] = Convert.ToByte(temp);
                }

                m_DataProtocol.m_DstAlertRelay.ExpPos[5] = m_DscMessage.ConvertExpnStringToNum(msg.expn_mode); // Expand Mode
            }

            m_DataProtocol.EncodeDistressAlertRelay(ref m_DataProtocol.m_DataFrame.data_buf);

            if (m_DataProtocol.CheckStatus() == PROTO_STAT.IDLE_STAT)
            {
                m_DataProtocol.SendDataFrame(cmd, cmd_param, m_DataProtocol.m_DataFrame.data_buf, DSC_MSG_SIZE);
                m_DataProtocol.SetProtocoStatus(PROTO_STAT.WAIT_FOR_ACK);
            }
        }

        private void SendDistressAlertRelayAck(DscCompose msg)
        {
            ushort cmd;
            byte cmd_type = DataProtocol.CMD_REQ;
            ushort cmd_param = DataProtocol.CMD_REQ_TX_DSC;
            cmd = m_DataProtocol.GenerateCMD(cmd_type, m_Sender, m_Receiver);

            m_DataProtocol.m_DstAlertRelayAck.Head.msg_type = MSG_TYPE.DSC_MSG_TYPE_RLY_ACK;

            // Format
            m_DataProtocol.m_DstAlertRelayAck.Format = m_DscMessage.ConvertFormatToNum(msg.msg_format);

            // Addr
            if (msg.mmsi_call_to.Equals("N/A"))
            {
                msg.mmsi_call_to = "0000000000";
            }
            else
            {
                if (msg.mmsi_call_to.Length == 9)
                {
                    msg.mmsi_call_to += "0";
                }
                else
                {
                    MessageBox.Show("Addr ID must be 9 digits.");
                    return;
                }
            }

            string temp;
            for (int i = 0; i < 5; i++)
            {
                temp = msg.mmsi_call_to.Substring(i * 2, 2);
                m_DataProtocol.m_DstAlertRelayAck.AddrID[i] = Convert.ToByte(temp);
            }

            // Category
            m_DataProtocol.m_DstAlertRelayAck.Category = m_DscMessage.ConvertCategoryStringToNum(msg.msg_category);

            // Self Id
            if (msg.mmsi_call_from.Length == 9)
            {
                msg.mmsi_call_from += "0";
            }
            else
            {
                MessageBox.Show("Self ID must be 9 digits.");
                return;
            }

            temp = null;
            for (int i = 0; i < 5; i++)
            {
                temp = msg.mmsi_call_from.Substring(i * 2, 2);
                m_DataProtocol.m_DstAlertRelayAck.SelfID[i] = Convert.ToByte(temp);
            }

            // telcom0
            m_DataProtocol.m_DstAlertRelayAck.TelCmd0 = m_DscMessage.ConvertTelCom1StringToNum(msg.teleCom);

            // distress id
            if (msg.mmsi_distress.Length == 9)
            {
                msg.mmsi_distress += "0";
            }
            else
            {
                MessageBox.Show("Distress ID must be 9 digits.");
                return;
            }

            temp = null;
            for (int i = 0; i < 5; i++)
            {
                temp = msg.mmsi_distress.Substring(i * 2, 2);
                m_DataProtocol.m_DstAlertRelayAck.DistID[i] = Convert.ToByte(temp);
            }

            // Nature
            m_DataProtocol.m_DstAlertRelayAck.Nature = m_DscMessage.ConvertNatureToNum(msg.nature);

            string position = null;
            string latitudeMinutesExpn = null;
            string longitudeMinutesExpn = null;

            DscComposeForm.EncodePos1(msg.latitude, msg.longitude, ref position, ref latitudeMinutesExpn, ref longitudeMinutesExpn);

            for (int i = 0; i < 5; i++)
            {
                temp = position.Substring(i * 2, 2);
                m_DataProtocol.m_DstAlertRelayAck.DstPos[i] = Convert.ToByte(temp);
            }

            string utc = null;
            DscComposeForm.EncodeUTC(msg.utc, ref utc);

            for (int i = 0; i < 2; i++)
            {
                temp = utc.Substring(i * 2, 2);
                m_DataProtocol.m_DstAlertRelayAck.UTC[i] = Convert.ToByte(temp);
            }

            // Sub Com
            m_DataProtocol.m_DstAlertRelayAck.SubCom = m_DscMessage.ConvertTelCom1StringToNum(msg.sub_com);

            // EOS
            m_DataProtocol.m_DstAlertRelayAck.EOS = m_DscMessage.ConvertEosStringToNum(msg.eos);

            // expand 
            //100,X1,X2,Y1,Y2,EOS
            if (longitudeMinutesExpn != null)
            {
                m_DataProtocol.m_DstAlertRelayAck.ExpPos[0] = 100;

                for (int i = 0; i < 2; i++) // latitude
                {
                    temp = latitudeMinutesExpn.Substring(i * 2, 2);
                    m_DataProtocol.m_DstAlertRelayAck.ExpPos[i + 1] = Convert.ToByte(temp);
                }

                for (int i = 0; i < 2; i++) // longitude
                {
                    temp = longitudeMinutesExpn.Substring(i * 2, 2);
                    m_DataProtocol.m_DstAlertRelayAck.ExpPos[i + 3] = Convert.ToByte(temp);
                }

                m_DataProtocol.m_DstAlertRelayAck.ExpPos[5] = m_DscMessage.ConvertExpnStringToNum(msg.expn_mode); // Expand Mode
            }

            m_DataProtocol.EncodeDistressAlertRelayAck(ref m_DataProtocol.m_DataFrame.data_buf);

            if (m_DataProtocol.CheckStatus() == PROTO_STAT.IDLE_STAT)
            {
                m_DataProtocol.SendDataFrame(cmd, cmd_param, m_DataProtocol.m_DataFrame.data_buf, DSC_MSG_SIZE);
                m_DataProtocol.SetProtocoStatus(PROTO_STAT.WAIT_FOR_ACK);
            }
        }

        private void SendAllShips(DscCompose msg)
        {
            ushort cmd = m_DataProtocol.GenerateCMD(DataProtocol.CMD_REQ, m_Sender, m_Receiver);
            ushort cmd_param = DataProtocol.CMD_REQ_TX_DSC;

            m_DataProtocol.m_AllShip.Head.msg_type = MSG_TYPE.DSC_MSG_TYPE_ALL_SHIP;
            m_DataProtocol.m_AllShip.Format = m_DscMessage.ConvertFormatToNum(msg.msg_format);
            m_DataProtocol.m_AllShip.Category = m_DscMessage.ConvertCategoryStringToNum(msg.msg_category);

            if (msg.mmsi_call_from.Length != 9)
            {
                MessageBox.Show("Self ID must be 9 digits.");
                return;
            }
            msg.mmsi_call_from += "0";

            for (int i = 0; i < 5; i++)
            {
                string temp = msg.mmsi_call_from.Substring(i * 2, 2);
                m_DataProtocol.m_AllShip.SelfID[i] = Convert.ToByte(temp);
            }

            m_DataProtocol.m_AllShip.TelCmd0 = m_DscMessage.ConvertTelCom1StringToNum(msg.teleCom1);
            m_DataProtocol.m_AllShip.TelCmd1 = m_DscMessage.ConvertTelCom2StringToNum(msg.teleCom2);

            string freq = DscComposeForm.EncodeFreq(msg.freq);
            for (int i = 0; i < 6; i++)
            {
                m_DataProtocol.m_AllShip.Freq[i] = Convert.ToByte(freq.Substring(i * 2, 2));
            }

            m_DataProtocol.m_AllShip.EOS = m_DscMessage.ConvertEosStringToNum(msg.eos);

            Array.Clear(m_DataProtocol.m_DataFrame.data_buf, 0, m_DataProtocol.m_DataFrame.data_buf.Length);
            m_DataProtocol.EncodeAllShips(ref m_DataProtocol.m_DataFrame.data_buf);

            if (m_DataProtocol.CheckStatus() == PROTO_STAT.IDLE_STAT)
            {
                m_DataProtocol.SendDataFrame(cmd, cmd_param, m_DataProtocol.m_DataFrame.data_buf, DSC_MSG_SIZE);
                m_DataProtocol.SetProtocoStatus(PROTO_STAT.WAIT_FOR_ACK);
            }
        }

        private void SendIndividual(DscCompose msg)
        {
            ushort cmd;
            byte cmd_type = DataProtocol.CMD_REQ;
            ushort cmd_param = DataProtocol.CMD_REQ_TX_DSC;
            cmd = m_DataProtocol.GenerateCMD(cmd_type, m_Sender, m_Receiver);

            m_DataProtocol.m_IndiCall.Head.msg_type = MSG_TYPE.DSC_MSG_TYPE_INDI_CALL;

            // Format
            m_DataProtocol.m_IndiCall.Format = m_DscMessage.ConvertFormatToNum(msg.msg_format);

            // Addr
            if (msg.mmsi_call_to.Equals("N/A"))
            {
                msg.mmsi_call_to = "0000000000";
            }
            else
            {
                if (msg.mmsi_call_to.Length == 9)
                {
                    msg.mmsi_call_to += "0";
                }
                else
                {
                    MessageBox.Show("Addr ID must be 9 digits.");
                    return;
                }
            }

            string temp;
            for (int i = 0; i < 5; i++)
            {
                temp = msg.mmsi_call_to.Substring(i * 2, 2);
                m_DataProtocol.m_IndiCall.AddrID[i] = Convert.ToByte(temp);
            }

            // Category
            m_DataProtocol.m_IndiCall.Category = m_DscMessage.ConvertCategoryStringToNum(msg.msg_category);

            // Self Id
            if (msg.mmsi_call_from.Length == 9)
            {
                msg.mmsi_call_from += "0";
            }
            else
            {
                MessageBox.Show("Self ID must be 9 digits.");
                return;
            }

            temp = null;
            for (int i = 0; i < 5; i++)
            {
                temp = msg.mmsi_call_from.Substring(i * 2, 2);
                m_DataProtocol.m_IndiCall.SelfID[i] = Convert.ToByte(temp);
            }

            // telcom0
            m_DataProtocol.m_IndiCall.TelCmd0 = m_DscMessage.ConvertTelCom1StringToNum(msg.teleCom1);
            m_DataProtocol.m_IndiCall.TelCmd1 = m_DscMessage.ConvertTelCom2StringToNum(msg.teleCom2);


            string freq = null;
            //string position = null;
            string latitudeMinutesExpn = null;
            string longitudeMinutesExpn = null;

            if (msg.freq.Equals("POS3") || msg.freq.Equals(TELCOM2_126))
            {
                for (int i = 0; i < 6; i++)
                {
                    m_DataProtocol.m_IndiCall.Freq[i] = 126;
                }
            }
            else
            {
                if (msg.freq.Equals("POS4"))
                {
                    DscComposeForm.EncodePos4(msg.latitude, msg.longitude, ref freq, ref latitudeMinutesExpn, ref longitudeMinutesExpn);

                    for (int i = 0; i < 5; i++)
                    {
                        temp = freq.Substring(i * 2, 2);
                        m_DataProtocol.m_IndiCall.Freq[i] = Convert.ToByte(temp);
                    }
                    m_DataProtocol.m_IndiCall.Freq[5] = 126;
                }
                else
                {
                    if (msg.freq == "126")
                    {
                        for (int i = 0; i < 6; i++)
                        {
                            temp = msg.freq;
                            m_DataProtocol.m_IndiCall.Freq[i] = Convert.ToByte(temp);
                        }
                    }
                    else
                    {
                        freq = DscComposeForm.EncodeFreq(msg.freq);

                        for (int i = 0; i < 6; i++)
                        {
                            temp = freq.Substring(i * 2, 2);
                            m_DataProtocol.m_IndiCall.Freq[i] = Convert.ToByte(temp);
                        }
                    }
                }
            }

            string utc = null;
            DscComposeForm.EncodeUTC(msg.utc, ref utc);

            for (int i = 0; i < 2; i++)
            {
                temp = utc.Substring(i * 2, 2);
                m_DataProtocol.m_IndiCall.UTC[i] = Convert.ToByte(temp);
            }

            // EOS
            m_DataProtocol.m_IndiCall.EOS = m_DscMessage.ConvertEosStringToNum(msg.eos);

            // expand 
            //100,X1,X2,Y1,Y2,EOS
            if (longitudeMinutesExpn != null)
            {
                m_DataProtocol.m_IndiCall.ExpPos[0] = 100;

                for (int i = 0; i < 2; i++) // latitude
                {
                    temp = latitudeMinutesExpn.Substring(i * 2, 2);
                    m_DataProtocol.m_IndiCall.ExpPos[i + 1] = Convert.ToByte(temp);
                }

                for (int i = 0; i < 2; i++) // longitude
                {
                    temp = longitudeMinutesExpn.Substring(i * 2, 2);
                    m_DataProtocol.m_IndiCall.ExpPos[i + 3] = Convert.ToByte(temp);
                }

                m_DataProtocol.m_IndiCall.ExpPos[5] = m_DscMessage.ConvertExpnStringToNum(msg.expn_mode); // Expand Mode
            }

            m_DataProtocol.EncodeIndividual(ref m_DataProtocol.m_DataFrame.data_buf);

            if (m_DataProtocol.CheckStatus() == PROTO_STAT.IDLE_STAT)
            {
                m_DataProtocol.SendDataFrame(cmd, cmd_param, m_DataProtocol.m_DataFrame.data_buf, DSC_MSG_SIZE);
                m_DataProtocol.SetProtocoStatus(PROTO_STAT.WAIT_FOR_ACK);
            }
        }

        private void SendGroupCall(DscCompose msg)
        {
            ushort cmd = m_DataProtocol.GenerateCMD(DataProtocol.CMD_REQ, m_Sender, m_Receiver);
            m_DataProtocol.m_GrpCall.Head.msg_type = MSG_TYPE.DSC_MSG_TYPE_GRP_CALL;
            m_DataProtocol.m_GrpCall.Format = m_DscMessage.ConvertFormatToNum(msg.msg_format);

            if (msg.mmsi_call_to.Equals("N/A"))
            {
                msg.mmsi_call_to = "0000000000";
            }
            else if (msg.mmsi_call_to.Length != 9)
            {
                MessageBox.Show("Addr ID must be 9 digits.");
                return;
            }
            msg.mmsi_call_to += "0";

            for (int i = 0; i < 5; i++)
            {
                string temp = msg.mmsi_call_to.Substring(i * 2, 2);
                m_DataProtocol.m_GrpCall.AddrID[i] = Convert.ToByte(temp);
            }

            m_DataProtocol.m_GrpCall.Category = m_DscMessage.ConvertCategoryStringToNum(msg.msg_category);

            if (msg.mmsi_call_from.Length != 9)
            {
                MessageBox.Show("Self ID must be 9 digits.");
                return;
            }
            msg.mmsi_call_from += "0";

            for (int i = 0; i < 5; i++)
            {
                string temp = msg.mmsi_call_from.Substring(i * 2, 2);
                m_DataProtocol.m_GrpCall.SelfID[i] = Convert.ToByte(temp);
            }

            m_DataProtocol.m_GrpCall.TelCmd0 = m_DscMessage.ConvertTelCom1StringToNum(msg.teleCom1);
            m_DataProtocol.m_GrpCall.TelCmd1 = m_DscMessage.ConvertTelCom2StringToNum(msg.teleCom2);

            string freq = DscComposeForm.EncodeFreq(msg.freq);
            for (int i = 0; i < 6; i++)
            {
                string temp = freq.Substring(i * 2, 2);
                m_DataProtocol.m_GrpCall.Freq[i] = Convert.ToByte(temp);
            }

            m_DataProtocol.m_GrpCall.EOS = m_DscMessage.ConvertEosStringToNum(msg.eos);
            m_DataProtocol.EncodeGroupCall(ref m_DataProtocol.m_DataFrame.data_buf);

            if (m_DataProtocol.CheckStatus() == PROTO_STAT.IDLE_STAT)
            {
                m_DataProtocol.SendDataFrame(cmd, DataProtocol.CMD_REQ_TX_DSC, m_DataProtocol.m_DataFrame.data_buf, DSC_MSG_SIZE);
                m_DataProtocol.SetProtocoStatus(PROTO_STAT.WAIT_FOR_ACK);
            }
        }

        private void SendSemiAuto(DscCompose msg)
        {
            ushort cmd = m_DataProtocol.GenerateCMD(DataProtocol.CMD_REQ, m_Sender, m_Receiver);
            m_DataProtocol.m_SemiAUTO.Head.msg_type = MSG_TYPE.DSC_MSG_TYPE_SEMI_AUTO;
            m_DataProtocol.m_SemiAUTO.Format = m_DscMessage.ConvertFormatToNum(msg.msg_format);

            if (msg.mmsi_call_to.Equals("N/A"))
            {
                msg.mmsi_call_to = "0000000000";
            }
            else if (msg.mmsi_call_to.Length != 9)
            {
                MessageBox.Show("Addr ID must be 9 digits.");
                return;
            }
            msg.mmsi_call_to += "0";

            for (int i = 0; i < 5; i++)
            {
                string temp = msg.mmsi_call_to.Substring(i * 2, 2);
                m_DataProtocol.m_SemiAUTO.AddrID[i] = Convert.ToByte(temp);
            }

            m_DataProtocol.m_SemiAUTO.Category = m_DscMessage.ConvertCategoryStringToNum(msg.msg_category);

            if (msg.mmsi_call_from.Length != 9)
            {
                MessageBox.Show("Self ID must be 9 digits.");
                return;
            }
            msg.mmsi_call_from += "0";

            for (int i = 0; i < 5; i++)
            {
                string temp = msg.mmsi_call_from.Substring(i * 2, 2);
                m_DataProtocol.m_SemiAUTO.SelfID[i] = Convert.ToByte(temp);
            }

            m_DataProtocol.m_SemiAUTO.TelCmd0 = m_DscMessage.ConvertTelCom1StringToNum(msg.teleCom1);
            m_DataProtocol.m_SemiAUTO.TelCmd1 = m_DscMessage.ConvertTelCom2StringToNum(msg.teleCom2);

            string freq = "9000" + msg.freq;
            for (int i = 0; i < 3; i++)
            {
                string temp = freq.Substring(i * 2, 2);
                m_DataProtocol.m_SemiAUTO.Freq[i] = Convert.ToByte(temp);
            }

            DscComposeForm.EncodePhoneNumber(ref m_DataProtocol.m_SemiAUTO.TelNo, 0, msg.mmsi_distress);
            m_DataProtocol.m_SemiAUTO.EOS = m_DscMessage.ConvertEosStringToNum(msg.eos);
            m_DataProtocol.EncodeSemiAuto(ref m_DataProtocol.m_DataFrame.data_buf);

            if (m_DataProtocol.CheckStatus() == PROTO_STAT.IDLE_STAT)
            {
                m_DataProtocol.SendDataFrame(cmd, DataProtocol.CMD_REQ_TX_DSC, m_DataProtocol.m_DataFrame.data_buf, DSC_MSG_SIZE);
                m_DataProtocol.SetProtocoStatus(PROTO_STAT.WAIT_FOR_ACK);
            }
        }


        private void CellClick_dgvDscSend(object sender, DataGridViewCellEventArgs e)
        {
            try
            {
                if (m_DataProtocol == null)
                {
                    MessageBox.Show("Please connect the serial port for data protocol.");
                    return;
                }

                if (e.ColumnIndex == SendDsc.Index) // Index of send button
                {
                    m_SendDscMsg.msg_type = dgvDscSend.Rows[e.RowIndex].Cells[0].Value.ToString();
                    m_SendDscMsg.msg_format = dgvDscSend.Rows[e.RowIndex].Cells[1].Value.ToString();
                    m_SendDscMsg.area = m_SendDscMsg.msg_format.Equals(FMT_102) ? dgvDscSend.Rows[e.RowIndex].Cells[2].Value.ToString() : null;
                    m_SendDscMsg.mmsi_call_to = m_SendDscMsg.msg_format.Equals(FMT_102) ? null : dgvDscSend.Rows[e.RowIndex].Cells[2].Value.ToString();
                    m_SendDscMsg.msg_category = dgvDscSend.Rows[e.RowIndex].Cells[3].Value.ToString();
                    m_SendDscMsg.mmsi_call_from = dgvDscSend.Rows[e.RowIndex].Cells[4].Value.ToString();
                    m_SendDscMsg.teleCom = dgvDscSend.Rows[e.RowIndex].Cells[5].Value.ToString();
                    m_SendDscMsg.teleCom1 = dgvDscSend.Rows[e.RowIndex].Cells[6].Value.ToString();
                    m_SendDscMsg.teleCom2 = dgvDscSend.Rows[e.RowIndex].Cells[7].Value.ToString();
                    m_SendDscMsg.sub_com = dgvDscSend.Rows[e.RowIndex].Cells[8].Value.ToString();
                    m_SendDscMsg.freq = dgvDscSend.Rows[e.RowIndex].Cells[9].Value.ToString();
                    m_SendDscMsg.mmsi_distress = dgvDscSend.Rows[e.RowIndex].Cells[10].Value.ToString();
                    m_SendDscMsg.nature = dgvDscSend.Rows[e.RowIndex].Cells[11].Value.ToString();
                    m_SendDscMsg.latitude = dgvDscSend.Rows[e.RowIndex].Cells[12].Value.ToString();
                    m_SendDscMsg.longitude = dgvDscSend.Rows[e.RowIndex].Cells[13].Value.ToString();
                    m_SendDscMsg.utc = dgvDscSend.Rows[e.RowIndex].Cells[14].Value.ToString();
                    m_SendDscMsg.eos = dgvDscSend.Rows[e.RowIndex].Cells[15].Value.ToString();
                    m_SendDscMsg.expn_mode = dgvDscSend.Rows[e.RowIndex].Cells[16].Value.ToString();

                    switch (m_SendDscMsg.msg_type)
                    {
                        case TYPE_DIST:
                            SendDistressAlert(m_SendDscMsg);
                            break;
                        case TYPE_DIST_ACK:
                            SendDistressAlertAck(m_SendDscMsg);
                            break;
                        case TYPE_DIST_RLY:
                            SendDistressAlertRelay(m_SendDscMsg);
                            break;
                        case TYPE_DIST_RLY_ACK:
                            SendDistressAlertRelayAck(m_SendDscMsg);
                            break;
                        case TYPE_ALLSHIPS:
                            SendAllShips(m_SendDscMsg);
                            break;
                        case TYPE_INDIVIDUAL:
                            SendIndividual(m_SendDscMsg);
                            break;
                        case TYPE_GROUP_CALL:
                            SendGroupCall(m_SendDscMsg);
                            break;
                        case TYPE_SEMI_AUTO:
                            SendSemiAuto(m_SendDscMsg);
                            break;
                    }
                }
            }
            catch (Exception)
            {
                MessageBox.Show("Column value is incorrect. Please enter the column value properly.");
            }
        }

        private void SelectedIndexChanged_lbAlert(object sender, EventArgs e)
        {
            var alertDetails = new[]
            {
                new { Id = "3008", Instance = "1", Priority = "WARNING", Category = "B", Title = "TX POWER:INHIBIT", Description = "Transmission inhibited" },
                new { Id = "3008", Instance = "2", Priority = "WARNING", Category = "B", Title = "TX POWER:UNLOCK", Description = "TX PLL is unlocked" },
                new { Id = "3115", Instance = "1", Priority = "WARNING", Category = "B", Title = "ANTENNA:FAILURE", Description = "SWR error" },
                new { Id = "3115", Instance = "2", Priority = "WARNING", Category = "B", Title = "RX:FAILURE", Description = "RX PLL is unlocked" },
                new { Id = "3115", Instance = "3", Priority = "WARNING", Category = "B", Title = "TU CONN LOST", Description = "Verify connection" }
            };

            if (lb_alert.SelectedIndex >= 0 && lb_alert.SelectedIndex < alertDetails.Length)
            { 
                var selectedAlert = alertDetails[lb_alert.SelectedIndex];
                tb_id.Text = selectedAlert.Id;
                tb_instance.Text = selectedAlert.Instance;
                tb_priority.Text = selectedAlert.Priority;
                tb_category.Text = selectedAlert.Category;
                tb_title.Text = selectedAlert.Title;
                tb_desc.Text = selectedAlert.Description;
            }

            bt_notify.Enabled = true;
        }

        private void bt_notify_Click(object sender, EventArgs e)
        {

            ushort alert_id = ushort.Parse(tb_id.Text);
            byte alert_instance = byte.Parse(tb_instance.Text);

            ushort cmd = m_DataProtocol.GenerateCMD(DataProtocol.CMD_SET_NOTI, DataProtocol.TRANSCEIVER, DataProtocol.CONTROLLER);

            m_DataProtocol.m_TuAlertData[(int)DataProtocol.TU_ALERT.ALERT_ID_L] = (byte)(alert_id & 0xFF);
            m_DataProtocol.m_TuAlertData[(int)DataProtocol.TU_ALERT.ALERT_ID_H] = (byte)(alert_id >> 8);
            m_DataProtocol.m_TuAlertData[(int)DataProtocol.TU_ALERT.ALERT_INSTANCE] = alert_instance;

            byte alert_state = rb_raise.Checked ? (byte)1 : (byte)2;
            m_DataProtocol.m_TuAlertData[(int)DataProtocol.TU_ALERT.ALERT_STATE] = alert_state;

            if (m_DataProtocol.CheckStatus() == PROTO_STAT.IDLE_STAT)
            {
                m_DataProtocol.SendDataFrame(cmd, DataProtocol.CMD_NOTI_TU_ALERT, m_DataProtocol.m_TuAlertData, (int)DataProtocol.TU_ALERT.MAX_TU_ALERT);
            }
        }

        private void front_mic_trackbar_scroll(object sender, EventArgs e)
        {
            byte trackBarValue = (byte)tb_front_mic.Value;
            string[] dbValues = new string[] { "-6dB", "-3dB", "0dB", "3dB", "6dB", "9dB", "12dB", "15dB", "18dB", "21dB", "24dB", "27dB" };
            lb_front_mic.Text = dbValues[trackBarValue];

            m_front_mk_value = trackBarValue;

            SendCodecSetFrameData((int)CODEC_SET.IN_AMP_FRONT_MIC, trackBarValue);
        }


        private void rear_mic_trackbar_scroll(object sender, EventArgs e)
        {
            byte trackBarValue = (byte)tb_rear_mic.Value;
            string[] dbValues = new string[] { "-6dB", "-3dB", "0dB", "3dB", "6dB", "9dB", "12dB", "15dB", "18dB", "21dB", "24dB", "27dB" };
            lb_rear_mic.Text = dbValues[trackBarValue];

            m_rear_mk_value = trackBarValue;

            SendCodecSetFrameData((int)CODEC_SET.IN_AMP_REAR_MIC, trackBarValue);
        }

        private void wing_mic_trackbar_scroll(object sender, EventArgs e)
        {
            byte trackBarValue = (byte)tb_wing_mic.Value;
            string[] dbValues = new string[] { "-6dB", "-3dB", "0dB", "3dB", "6dB", "9dB", "12dB", "15dB", "18dB", "21dB", "24dB", "27dB" };
            lb_wing_mic.Text = dbValues[trackBarValue];

            m_wing_mk_value = trackBarValue;

            SendCodecSetFrameData((int)CODEC_SET.IN_AMP_WING_MIC, trackBarValue);
        }

        private void rx_bb_trackbar_scroll(object sender, EventArgs e)
        {
            byte trackBarValue = (byte)tb_rx_bb.Value;
            string[] dbValues = new string[] { "-6dB", "-3dB", "0dB", "3dB", "6dB", "9dB", "12dB", "15dB", "18dB", "21dB", "24dB", "27dB" };
            lb_rx_bb.Text = dbValues[trackBarValue];

            m_rx_bb_value = trackBarValue;
            SendCodecSetFrameData((int)CODEC_SET.IN_AMP_RX_BB, trackBarValue);
        }

        private void front_h_spk_trackbar_scroll(object sender, EventArgs e)
        {
            byte trackBarValue = (byte)tb_front_h_spk.Value;
            string[] dbValues = new string[] { "0dB", "0.5dB", "1dB", "1.5dB", "2dB", "2.5dB", "3dB", "3.5dB", "4dB", "4.5dB", "5dB", "5.5dB", "6dB", "6.5dB", "7dB", "7.5dB", "8dB", "8.5dB", "9dB", "9.5dB", "10dB", "10.5dB", "11dB", "11.5dB", "12dB" };
            lb_front_h_spk.Text = dbValues[trackBarValue];

            m_front_h_spk_value = trackBarValue;

            SendVolSetFrameData((byte)VOL_CH.FRONT_H_SPK, (int)VOL_SET.OUT_FRONT_H_SPK, (byte)(0x18 - trackBarValue));
        }
        private void rear_wing_h_spk_trackbar_scroll(object sender, EventArgs e)
        {
            byte trackBarValue = (byte)tb_rear_wing_h_spk.Value;
            string[] dbValues = new string[] { "0dB", "0.5dB", "1dB", "1.5dB", "2dB", "2.5dB", "3dB", "3.5dB", "4dB", "4.5dB", "5dB", "5.5dB", "6dB", "6.5dB", "7dB", "7.5dB", "8dB", "8.5dB", "9dB", "9.5dB", "10dB", "10.5dB", "11dB", "11.5dB", "12dB" };
            lb_rear_wing_h_spk.Text = dbValues[trackBarValue];

            m_rear_wing_h_spk_value = trackBarValue;

            SendVolSetFrameData((byte)VOL_CH.REAR_WING_H_SPK, (int)VOL_SET.OUT_REAR_WING_SPK, (byte)(0x18 - trackBarValue));
        }

        private void front_ext_spk_trackbar_scroll(object sender, EventArgs e)
        {
            byte trackBarValue = (byte)tb_front_ext_spk.Value;
            string[] dbValues = new string[] { "0dB", "0.5dB", "1dB", "1.5dB", "2dB", "2.5dB", "3dB", "3.5dB", "4dB", "4.5dB", "5dB", "5.5dB", "6dB", "6.5dB", "7dB", "7.5dB", "8dB", "8.5dB", "9dB", "9.5dB", "10dB", "10.5dB", "11dB", "11.5dB", "12dB" };
            lb_front_ext_spk.Text = dbValues[trackBarValue];

            m_wing_mk_value = trackBarValue;

            SendVolSetFrameData((byte)VOL_CH.FRONT_EXT_SPK, (int)VOL_SET.OUT_FRONT_EXT_SPK, (byte)(0x18 - trackBarValue));
        }

        private void out_rx_bb_trackbar_scroll(object sender, EventArgs e)
        {
            byte trackBarValue = (byte)tb_out_rx_bb.Value;
            string[] dbValues = new string[] { "0dB", "0.5dB", "1dB", "1.5dB", "2dB", "2.5dB", "3dB", "3.5dB", "4dB", "4.5dB", "5dB", "5.5dB", "6dB", "6.5dB", "7dB", "7.5dB", "8dB", "8.5dB", "9dB", "9.5dB", "10dB", "10.5dB", "11dB", "11.5dB", "12dB" };
            lb_out_rx_bb.Text = dbValues[trackBarValue];

            m_rx_bb_out_value = trackBarValue;

            SendVolSetFrameData((byte)VOL_CH.OUT_RX_BB, (int)VOL_SET.OUT_RX_BB, (byte)(0x18 - trackBarValue));
        }


        private void tb_wkr_freq_keydown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                bool isValid = float.TryParse(tb_wkr_freq.Text, out float freq);
                if (!isValid || freq < 10.0f || freq > 200.0f)
                {
                    MessageBox.Show("FEQ : 10.0MHz <= value <= 200.0MHz is range, entering value is wrong", "Input Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    tb_wkr_freq.Clear();
                    tb_wkr_freq.Focus();
                }
                else
                {
                    try
                    {
                        m_DataProtocol.m_SetRfData.WkrDdsFreq = (uint)Math.Round(freq * 1000);
                        SendRF_DdsFreqData();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show("An error occurred: " + ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        tb_wkr_freq.Clear();
                        tb_wkr_freq.Focus();
                    }
                }
                e.Handled = true;
                e.SuppressKeyPress = true;
            }

        }

        private void main_tx_power_trackbar_scroll(object sender, EventArgs e)
        {
            ushort trackBarValue = (ushort)tb_main_tx_pwr.Value;
            m_DataProtocol.m_SetRfData.MainTxPower = trackBarValue;
            lb_main_tx_pwr.Text = trackBarValue.ToString();
            SendRF_DdsFreqData();
            Thread.Sleep(100);
        }

        private void main_rx_power_trackbar_scroll(object sender, EventArgs e)
        {
            ushort trackBarValue = (ushort)tb_main_rx_pwr.Value;
            m_DataProtocol.m_SetRfData.MainRxPower = trackBarValue;
            lb_main_rx_pwr.Text = trackBarValue.ToString();
            SendRF_DdsFreqData();
            Thread.Sleep(100);
        }

        private void wkr_rx_power_trackbar_scroll(object sender, EventArgs e)
        {
            ushort trackBarValue = (ushort)tb_wkr_rx_pwr.Value;
            m_DataProtocol.m_SetRfData.WkrRxPower = trackBarValue;
            lb_wkr_rx_pwr.Text = trackBarValue.ToString();
            SendRF_DdsFreqData();
            Thread.Sleep(100);
        }

        private void tb_pwr_cnt_keydown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                float pwr_cnt;
                if (float.TryParse(tb_pwr_cnt.Text, out pwr_cnt) && pwr_cnt >= 0.0f && pwr_cnt <= 3.3f)
                {
                    m_DataProtocol.m_SetRfData.PwrCnt = (byte)Math.Round(pwr_cnt * 10);
                    SendRF_DdsFreqData();
                    e.Handled = true;
                    e.SuppressKeyPress = true;
                }
                else
                {
                    MessageBox.Show("PWR CNT : 0.0v <= value <= 3.3v is range, entering value is wrong", "Input Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    tb_pwr_cnt.Clear();
                    tb_pwr_cnt.Focus();
                }
            }

        }

        private void tb_dds_adj_keydown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                float dds_adj;
                if (float.TryParse(tb_dds_adj.Text, out dds_adj) && dds_adj >= 0.0f && dds_adj <= 100.0f)
                {
                    m_DataProtocol.m_SetRfData.DdsAdj = (byte)Math.Round(Math.Round(dds_adj, 2) * 100);
                    SendRF_DdsFreqData();
                    e.Handled = true;
                    e.SuppressKeyPress = true;
                }
                else
                {
                    MessageBox.Show("DDS ADJ : 0.0 <= value <= 100.0 is range, entering value is wrong", "Input Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    tb_dds_adj.Clear();
                    tb_dds_adj.Focus();
                }
            }
        }

        private void bt_rx_ext_full_clear_Click(object sender, EventArgs e)
        {
            rich_tb_full_sentence.Clear();
        }

        private void bt_rx_ext_full_save_Click(object sender, EventArgs e)
        {
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.Filter = "Text Files (*.txt)|*.txt|All Files (*.*)|*.*";
            dlg.FilterIndex = 1;
            dlg.RestoreDirectory = true;
            dlg.AddExtension = true;
            dlg.DefaultExt = "txt";

            if (dlg.ShowDialog() == DialogResult.OK)
            {
                this.Text = dlg.FileName;
                using (StreamWriter sw = new StreamWriter(dlg.FileName))
                {
                    sw.Write(rich_tb_full_sentence.Text);
                }
            }
        }

        private void tb_main_rx_freq_keydown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                if (!float.TryParse(tb_main_rx_freq.Text, out float freq) || freq < 10.0f || freq > 200.0f)
                {
                    MessageBox.Show("FEQ : 10.0MHz <= value <= 200.0MHz is range, entering value is wrong", "Input Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    tb_main_rx_freq.Clear();
                    tb_main_rx_freq.Focus();
                }
                else
                {
                    m_DataProtocol.m_SetRfData.MainDdsFreq = (uint)Math.Round(freq * 1000);
                    SendRF_DdsFreqData();
                    e.Handled = true;
                    e.SuppressKeyPress = true;
                }
            }
        }

        private void tb_main_tx_freq_keydown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                if (!float.TryParse(tb_main_tx_freq.Text, out float freq) || freq < 10.0f || freq > 200.0f)
                {
                    MessageBox.Show("FEQ : 10.0MHz <= value <= 200.0MHz is range, entering value is wrong", "Input Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    tb_main_tx_freq.Clear();
                    tb_main_tx_freq.Focus();
                }
                else
                {
                    m_DataProtocol.m_SetRfData.MainDdsFreq = (uint)Math.Round(freq * 1000);
                    SendRF_DdsFreqData();
                    e.Handled = true;
                    e.SuppressKeyPress = true;
                }
            }
        }

        private void tb_dcoffset_keydown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                if (float.TryParse(tb_dcoffset.Text, out float dc_offset) && dc_offset >= -1.0f && dc_offset <= 1.0f)
                {
                    m_DataProtocol.m_SetRfData.DcOffset = (byte)Math.Round((Math.Round(dc_offset, 2) + 1.0f) * 100);
                    SendRF_DdsFreqData();
                    e.Handled = true;
                    e.SuppressKeyPress = true;
                }
                else
                {
                    MessageBox.Show("DC OFFSET : -1.0 <= value <= 1.0 is range, entering value is wrong", "Input Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    tb_dcoffset.Clear();
                    tb_dcoffset.Focus();
                }
            }
        }

        private void bt_get_audiotrack_Click(object sender, EventArgs e)
        {
            SendReqHeaderOnly(CMD_REQ_TRACK_INFO);
            m_stopwatch.Reset();
        }

        private void bt_playback_Click(object sender, EventArgs e)
        {
            if (bt_playback.Text == "PAUSE")
            {
                SendAudioTrackSetFrameData(m_track_number, 1, m_playbackTime, 0); // STOP (0), PAUSE(1), RECORDING(2), PLAYBACK(3)
                bt_playback.Text = "PLAY";
                bt_playback.BackColor = Color.Transparent;
                AudioTrack_timer.Stop();
                m_stopwatch.Stop();
            }
            else if (bt_playback.Text == "PLAY")
            {
                SendAudioTrackSetFrameData(m_track_number, 3, m_playbackTime, 0); // STOP (0), PAUSE(1), RECORDING(2), PLAYBACK(3)
                bt_playback.Text = "PAUSE";
                bt_playback.BackColor = Color.LightGreen;
                m_stopwatch.Start();
                AudioTrack_timer.Start();
            }
        }

        private void SelectedIndexChanged_Track(object sender, EventArgs e)
        {
            if (lb_audiotrack.SelectedIndex != -1)
            {
                ResetAudtioTrackSet();
                bt_playback.Enabled = true;
                bt_delete_track.Enabled = true;
                m_track_number = byte.Parse(lb_audiotrack.Text.Split('[')[1].Split(']')[0]);

                SendReadyTrackSetFrameData((int)READY_TRACK_SET.TRACK_NUMBER, m_track_number);
                tb_audiotrack.Maximum = (int)(m_AudioTrackRecordMS[m_track_number]);

                lb_rec_time.Text = "Record Time: " + (m_AudioTrackRecordMS[m_track_number] / 1000.0).ToString("F3") + "s";
            }
            else
            {
                ResetAudioTrackWidget(false);
            }
        }

        private void bt_delete_all_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("Do you want to delete all tracks?", "Delete Confirmation", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                SendDeleteTrackSetFrameData((int)DELETE_TRACK_SET.TRACK_NUMBER, 0xff);
                ResetAudioTrackWidget(false);
            }
        }

        private void bt_delete_track_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show($"Do you want to delete track {m_track_number}?", "Delete Confirmation", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                SendDeleteTrackSetFrameData((int)DELETE_TRACK_SET.TRACK_NUMBER, m_track_number);
                lb_audiotrack.Items.RemoveAt(lb_audiotrack.SelectedIndex);
                bt_playback.Enabled = false;
                bt_delete_track.Enabled = lb_audiotrack.Items.Count > 0;
            }
        }

        private void AudioTrack_timer_Tick(object sender, EventArgs e)
        {
            if (tb_audiotrack.Value >= tb_audiotrack.Maximum)
            {

                ResetAudtioTrackSet();
            }
            else
            {
                m_playbackTime = ((m_new_playbackTime + (int)m_stopwatch.ElapsedMilliseconds) / 10) * 10;

                if (m_playbackTime > tb_audiotrack.Maximum)
                    tb_audiotrack.Value = tb_audiotrack.Maximum;
                else
                    tb_audiotrack.Value = m_playbackTime;

                lb_playback_time.Text = "Playback Time: " + (m_playbackTime / 1000.0).ToString("F2") + "s";
                lb_playback_time.ForeColor = Color.Blue;
            }
        }

        private void audiotrack_trackbar_scroll(object sender, EventArgs e)
        {
            m_playbackTime = m_new_playbackTime = (tb_audiotrack.Value / 10) * 10;
            lb_playback_time.Text = "Playback Time: " + (m_new_playbackTime / 1000.0).ToString("F2") + "s";
            lb_playback_time.ForeColor = Color.Blue;
            m_stopwatch.Reset();
            AudioTrack_timer.Stop();
            m_stopwatch.Stop();
            bt_playback.Text = "PLAY";
            bt_playback.BackColor = Color.Transparent;
        }

        private void ResetAudtioTrackSet()
        {
            tb_audiotrack.Value = 0;
            lb_playback_time.Text = "Playback Time: 0.000s";
            lb_playback_time.ForeColor = Color.Black;
            m_stopwatch.Reset();
            AudioTrack_timer.Stop();
            m_stopwatch.Stop();
            bt_playback.Text = "PLAY";
            bt_playback.BackColor = Color.Transparent;
            m_playbackTime = 0;
            m_new_playbackTime = 0;
        }

        private void bt_record_Click(object sender, EventArgs e)
        {
            if (bt_record.Text == "REC OFF")
            {
                SendAudioTrackSetFrameData(m_track_number, 2, 0, 1); // STOP (0), PAUSE(1), RECORDING(2), PLAYBACK(3)
                bt_record.BackColor = Color.Red;
                bt_record.ForeColor = Color.White;
                ResetAudtioTrackSet();
                bt_record.Text = "REC ON";
            }
            else
            {
                SendAudioTrackSetFrameData(m_track_number, 2, 0, 0); // STOP (0), PAUSE(1), RECORDING(2), PLAYBACK(3)
                bt_record.Text = "REC OFF";
                bt_record.BackColor = Color.Transparent;
                bt_record.ForeColor = Color.Black;
            }

        }

        private void btnSetController_Click(object sender, EventArgs e)
        {
            SendControllerSetFrameData((int)CONTROLLER_SET.SOURCE, 0x00);
            m_DataProtocol.m_ConnectStat = false;

            TabPageOnOff(false);
            m_sys_info.trans_connected = false;
            btnSetController.Enabled = false;
        }

        private void btn_run_Click(object sender, EventArgs e)
        {
            if (m_selfTestToggle)
            {
                // SendSelftestFrameData((int)DSC_LOOP_TEST.TEST_RESULT, 1, 1);               
                btn_run.Text = "START";
                if (m_selfTestThread != null && m_selfTestThread.IsAlive)
                {
                    m_selfTestThread.Abort();
                }
            }
            else
            {
                btn_run.Text = "STOP";
                m_selfTestThread = new Thread(SelfTestStartThread);
                m_selfTestThread.Start();

            }
            m_selfTestToggle = !m_selfTestToggle;
        }

        private void SelfTestStartThread()
        {
            while (true)
            {
                SendSelftestFrameData((int)DSC_LOOP_TEST.TEST_RESULT, 1, 1);
                Thread.Sleep(1000);
            }
        }

        private void btn_selftest_clear_Click(object sender, EventArgs e)
        {
            dgv_selftest.Rows.Clear();

            lb_selftest_tot_pass.Text = "PASS : 0";
            lb_selftest_tot_fail.Text = "FAIL : 0";

            m_selftest_tot_pass_count = 0;
            m_selftest_tot_fail_count = 0;
        }

        private void btn_selftest_export_Click(object sender, EventArgs e)
        {
            using (SaveFileDialog dlg = new SaveFileDialog())
            {
                dlg.Filter = "csv (*.csv) | *.csv";
                if (dlg.ShowDialog() == DialogResult.OK)
                {
                    Utill.Save_Csv(dlg.FileName, dgv_selftest, true);
                }
            }
        }

        private void btn_dsc_auto_send_Click(object sender, EventArgs e)
        {
            int auto_send_interval = int.Parse(tb_dsc_send_auto_intval.Text);

            if (auto_send_interval < 1000 || auto_send_interval > 5000)
            {
                MessageBox.Show("interval : 1000 <= ms <= 5000 is range, entering value is wrong", "Input Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                tb_dsc_send_auto_intval.Text = "1000";
                tb_dsc_send_auto_intval.Focus();
            }

            dsc_send_timer.Interval = auto_send_interval;

            m_dsc_send_info.num_of_runs = int.Parse(tb_num_of_runs.Text);

            m_dsc_send_info.sent_count = 0;

            if (m_dsc_send_info.auto_send_toggle)
            {
                btn_dsc_auto_send.Text = "RUN";
                dsc_send_timer.Stop();
            }
            else
            {
                m_dsc_send_info.sent_count = 0;
                btn_dsc_auto_send.Text = "STOP";
                dsc_send_timer.Start();
            }
            m_dsc_send_info.auto_send_toggle = !m_dsc_send_info.auto_send_toggle;
        }
        private void SendDscCall(int rowIndex)
        {
            m_SendDscMsg.msg_type = dgvDscSend.Rows[rowIndex].Cells[0].Value.ToString();
            m_SendDscMsg.msg_format = dgvDscSend.Rows[rowIndex].Cells[1].Value.ToString();
            m_SendDscMsg.area = m_SendDscMsg.msg_format.Equals(FMT_102) ? dgvDscSend.Rows[rowIndex].Cells[2].Value.ToString() : null;
            m_SendDscMsg.mmsi_call_to = m_SendDscMsg.msg_format.Equals(FMT_102) ? null : dgvDscSend.Rows[rowIndex].Cells[2].Value.ToString();
            m_SendDscMsg.msg_category = dgvDscSend.Rows[rowIndex].Cells[3].Value.ToString();
            m_SendDscMsg.mmsi_call_from = dgvDscSend.Rows[rowIndex].Cells[4].Value.ToString();
            m_SendDscMsg.teleCom = dgvDscSend.Rows[rowIndex].Cells[5].Value.ToString();
            m_SendDscMsg.teleCom1 = dgvDscSend.Rows[rowIndex].Cells[6].Value.ToString();
            m_SendDscMsg.teleCom2 = dgvDscSend.Rows[rowIndex].Cells[7].Value.ToString();
            m_SendDscMsg.sub_com = dgvDscSend.Rows[rowIndex].Cells[8].Value.ToString();
            m_SendDscMsg.freq = dgvDscSend.Rows[rowIndex].Cells[9].Value.ToString();
            m_SendDscMsg.mmsi_distress = dgvDscSend.Rows[rowIndex].Cells[10].Value.ToString();
            m_SendDscMsg.nature = dgvDscSend.Rows[rowIndex].Cells[11].Value.ToString();
            m_SendDscMsg.latitude = dgvDscSend.Rows[rowIndex].Cells[12].Value.ToString();
            m_SendDscMsg.longitude = dgvDscSend.Rows[rowIndex].Cells[13].Value.ToString();
            m_SendDscMsg.utc = dgvDscSend.Rows[rowIndex].Cells[14].Value.ToString();
            m_SendDscMsg.eos = dgvDscSend.Rows[rowIndex].Cells[15].Value.ToString();
            m_SendDscMsg.expn_mode = dgvDscSend.Rows[rowIndex].Cells[16].Value.ToString();

            switch (m_SendDscMsg.msg_type)
            {
                case TYPE_DIST:
                    SendDistressAlert(m_SendDscMsg);
                    break;
                case TYPE_DIST_ACK:
                    SendDistressAlertAck(m_SendDscMsg);
                    break;
                case TYPE_DIST_RLY:
                    SendDistressAlertRelay(m_SendDscMsg);
                    break;
                case TYPE_DIST_RLY_ACK:
                    SendDistressAlertRelayAck(m_SendDscMsg);
                    break;
                case TYPE_ALLSHIPS:
                    SendAllShips(m_SendDscMsg);
                    break;
                case TYPE_INDIVIDUAL:
                    SendIndividual(m_SendDscMsg);
                    break;
                case TYPE_GROUP_CALL:
                    SendGroupCall(m_SendDscMsg);
                    break;
                case TYPE_SEMI_AUTO:
                    SendSemiAuto(m_SendDscMsg);
                    break;
            }
        }

        private void DscSendTimerHandler(object sender, EventArgs e)
        {
            for (int rowIndex = 0; rowIndex < dgvDscSend.Rows.Count; rowIndex++)
            {
                SendDscCall(rowIndex);
                //Thread.Sleep(100);
            }

            m_dsc_send_info.sent_count++;

            btn_dsc_auto_send.Text = "STOP (" + m_dsc_send_info.sent_count.ToString() + "/" + m_dsc_send_info.num_of_runs.ToString() + ")";            

            if (cb_keep_run.Checked == false)
            {
                if (m_dsc_send_info.sent_count >= m_dsc_send_info.num_of_runs)
                {
                    m_dsc_send_info.auto_send_toggle = !m_dsc_send_info.auto_send_toggle;
                    btn_dsc_auto_send.Text = "RUN";                
                    dsc_send_timer.Stop();
                }
            }
        }

        private void tb_dsc_test_interval_keydown(object sender, KeyEventArgs e)
        {

            if (e.KeyCode == Keys.Enter)
            {
                int interval;
                if (int.TryParse(tb_dsc_send_auto_intval.Text, out interval) && interval >= 1000 && interval <= 5000)
                {
                    e.Handled = true;
                    e.SuppressKeyPress = true;
                }
                else
                {
                    MessageBox.Show("interval : 1000 <= ms <= 5000 is range, entering value is wrong", "Input Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    tb_dsc_send_auto_intval.Text = "1000";
                    tb_dsc_send_auto_intval.Focus();
                }
            }

        }

        private void tb_dsc_test_num_of_run_keydown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                int num_of_run;
                if (int.TryParse(tb_num_of_runs.Text, out num_of_run) && num_of_run >= 1 && num_of_run <= 300)
                {
                    e.Handled = true;
                    e.SuppressKeyPress = true;
                }
                else
                {
                    MessageBox.Show("DDS ADJ : value > 0 and value <= 300 is range, entering value is wrong", "Input Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    tb_num_of_runs.Text = "100";
                    tb_num_of_runs.Focus();
                }
            }

        }
    }
}
